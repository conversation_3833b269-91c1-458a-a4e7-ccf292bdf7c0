import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';
import { aiService } from '@/services/ai-service';
import { geminiFileService } from '@/services/gemini-file-service';
import { z } from 'zod';
import { detectLanguage, getLanguageInstructions, validateLanguageConsistency, getLanguageName, type SupportedLanguage } from '@/utils/language-detection';
import { GeminiFileContext } from '@/types/proposal';

// Request validation schema
const enhanceChartSchema = z.object({
  enhancementType: z.enum(['make_shorter', 'make_longer', 'change_tone', 'custom_enhance']),
  toneType: z.enum(['professional', 'casual', 'technical']).optional(),
  customPrompt: z.string().optional(),
  selectedFileIds: z.array(z.string()).optional(),
  locale: z.enum(['en', 'ar']).optional()
});

export type ChartEnhancementType = 'make_shorter' | 'make_longer' | 'change_tone' | 'custom_enhance';
export type ChartToneType = 'professional' | 'casual' | 'technical';

interface ChartEnhancementResponse {
  originalContent: string;
  enhancedContent: string;
  originalDescription?: string;
  enhancedDescription?: string;
  enhancementType: ChartEnhancementType;
  toneType?: ChartToneType;
  customPrompt?: string;
  explanation: string;
  tokensUsed: number;
  detectedLanguage: SupportedLanguage;
  languageConsistency: {
    isConsistent: boolean;
    originalLang: SupportedLanguage;
    enhancedLang: SupportedLanguage;
    warning?: string;
  };
}

/**
 * POST /api/charts/[id]/enhance
 * Enhance chart content using AI
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for chart updating
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.UPDATE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id: chartId } = params;
    const body = await request.json();
    
    // Validate request data
    const validatedData = enhanceChartSchema.parse(body);

    // Verify chart exists and belongs to tenant
    const existingChart = await prisma.proposalChart.findFirst({
      where: {
        id: chartId,
        tenantId: currentTenant.id
      },
      include: {
        proposal: {
          include: {
            opportunity: true
          }
        }
      }
    });

    if (!existingChart) {
      return NextResponse.json(
        { error: 'Chart not found or access denied' },
        { status: 404 }
      );
    }

    if (!existingChart.mermaidCode) {
      return NextResponse.json(
        { error: 'Chart has no content to enhance' },
        { status: 400 }
      );
    }

    // Use provided locale or detect the language of the original content
    const proposalLocale = validatedData.locale || (existingChart.proposal.locale as 'en' | 'ar') || 'en';
    const detectedLanguage = proposalLocale === 'ar' ? 'ar' : detectLanguage(existingChart.mermaidCode + ' ' + (existingChart.description || ''));
    console.log(`Using language for chart ${chartId}:`, detectedLanguage, `(proposal locale: ${proposalLocale})`);

    // Get file contexts if file IDs are provided
    let fileContexts: GeminiFileContext[] = [];
    let fileUris: string[] = [];
    
    if (validatedData.selectedFileIds && validatedData.selectedFileIds.length > 0) {
      try {
        // Get proposal's Gemini file URIs
        const proposal = await prisma.proposal.findFirst({
          where: {
            id: existingChart.proposalId,
            tenantId: currentTenant.id
          }
        });

        const geminiFileUris = (proposal?.geminiFileUris as Record<string, string>) || {};
        
        // Get file contexts for the selected files
        fileContexts = await geminiFileService.getFileContexts(
          validatedData.selectedFileIds,
          currentTenant.id,
          geminiFileUris
        );

        // Extract active file URIs
        fileUris = fileContexts
          .filter(ctx => ctx.fileUri && ctx.uploadStatus === 'active')
          .map(ctx => ctx.fileUri);

        console.log(`Using ${fileUris.length} file contexts for chart enhancement`);
      } catch (error) {
        console.warn('Failed to load file contexts for chart enhancement:', error);
        // Continue without file context rather than failing
      }
    }

    // Generate enhancement prompt for chart
    const enhancementPrompt = generateChartEnhancementPrompt(
      existingChart.mermaidCode,
      existingChart.description || '',
      existingChart.title,
      validatedData.enhancementType,
      validatedData.toneType,
      detectedLanguage,
      validatedData.customPrompt,
      fileContexts
    );

    // Call AI service for enhancement
    const aiResponse = await Promise.race([
      aiService.generateResponse({
        prompt: enhancementPrompt,
        systemPrompt: getChartEnhancementSystemPrompt(detectedLanguage, validatedData.enhancementType),
        fileUris: fileUris.length > 0 ? fileUris : undefined,
        context: {
          chartType: existingChart.chartType,
          chartTitle: existingChart.title,
          proposalTitle: existingChart.proposal.title,
          opportunityName: existingChart.proposal.opportunity?.description || 'Unknown Opportunity',
          enhancementType: validatedData.enhancementType,
          toneType: validatedData.toneType,
          fileContexts
        },
        tenantId: currentTenant.id,
        userId: session.user.id,
        feature: 'chart_enhancement'
      }, {
        temperature: 0.6,
        maxTokens: 1500,
        model: 'gemini-2.0-flash'
      }),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('AI request timeout')), 30000) // 30 second timeout for charts
      )
    ]);

    // Parse the enhanced content
    const { enhancedMermaidCode, enhancedDescription } = parseEnhancedChartContent(aiResponse.content);

    // Validate language consistency
    const combinedOriginal = existingChart.mermaidCode + ' ' + (existingChart.description || '');
    const combinedEnhanced = enhancedMermaidCode + ' ' + (enhancedDescription || '');
    const languageConsistency = validateLanguageConsistency(combinedOriginal, combinedEnhanced);

    // Log language validation results
    if (!languageConsistency.isConsistent) {
      console.warn(`Language consistency warning for chart ${chartId}:`, languageConsistency.warning);
    }

    const response: ChartEnhancementResponse = {
      originalContent: existingChart.mermaidCode,
      enhancedContent: enhancedMermaidCode,
      originalDescription: existingChart.description || undefined,
      enhancedDescription: enhancedDescription || undefined,
      enhancementType: validatedData.enhancementType,
      toneType: validatedData.toneType,
      customPrompt: validatedData.customPrompt,
      explanation: getChartEnhancementExplanation(validatedData.enhancementType, validatedData.toneType, detectedLanguage, validatedData.customPrompt),
      tokensUsed: aiResponse.tokensUsed,
      detectedLanguage,
      languageConsistency
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Chart enhancement error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    // Handle timeout errors specifically
    if (error instanceof Error && error.message.includes('timeout')) {
      return NextResponse.json(
        { error: 'Chart enhancement request timed out. Please try again.' },
        { status: 408 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to enhance chart content. Please try again.' },
      { status: 500 }
    );
  }
}

function generateChartEnhancementPrompt(
  mermaidCode: string,
  description: string,
  title: string,
  enhancementType: ChartEnhancementType,
  toneType: ChartToneType | undefined,
  detectedLanguage: SupportedLanguage,
  customPrompt?: string,
  fileContexts?: GeminiFileContext[]
): string {
  // For custom enhancements, check if user is requesting a language change
  const isLanguageChangeRequest = enhancementType === 'custom_enhance' && customPrompt && (
    customPrompt.toLowerCase().includes('arabic') ||
    customPrompt.toLowerCase().includes('عربي') ||
    customPrompt.toLowerCase().includes('باللغه العربيه') ||
    customPrompt.toLowerCase().includes('باللغة العربية') ||
    customPrompt.toLowerCase().includes('اعاده كتابه') ||
    customPrompt.toLowerCase().includes('إعادة كتابة') ||
    customPrompt.toLowerCase().includes('english') ||
    customPrompt.toLowerCase().includes('translate') ||
    customPrompt.toLowerCase().includes('rewrite in')
  );

  // Get language-specific instructions, but be flexible for custom enhancements
  const languageInstructions = enhancementType === 'custom_enhance'
    ? (isLanguageChangeRequest 
        ? 'Follow the user\'s language instructions precisely. If they ask to rewrite in a specific language, do so completely.' 
        : 'Maintain the original language unless the user specifically requests otherwise.')
    : getLanguageInstructions(detectedLanguage);

  let basePrompt = '';
  const contentToEnhance = `Chart Title: ${title}
Mermaid Code:
${mermaidCode}
${description ? `\nDescription: ${description}` : ''}`;

  switch (enhancementType) {
    case 'make_shorter':
      basePrompt = `Simplify this Mermaid chart by reducing complexity while keeping essential elements:

${contentToEnhance}

Make the chart 20-30% simpler by removing non-essential nodes or relationships.

${languageInstructions}`;
      break;

    case 'make_longer':
      basePrompt = `Expand this Mermaid chart with additional relevant details and components:

${contentToEnhance}

Add 30-50% more relevant nodes, relationships, or details while maintaining clarity.

${languageInstructions}`;
      break;

    case 'change_tone':
      const toneMap = {
        professional: 'formal business terminology',
        casual: 'simple, accessible language',
        technical: 'detailed technical terminology'
      };

      basePrompt = `Rewrite this Mermaid chart using ${toneMap[toneType || 'professional']}:

${contentToEnhance}

Adjust the labels and descriptions while maintaining the chart structure.

${languageInstructions}`;
      break;

    case 'custom_enhance':
      basePrompt = `Instructions: ${customPrompt || 'Improve the chart quality and clarity'}

Chart to enhance:
${contentToEnhance}

${languageInstructions}`;
      break;
  }

  // Add file context if available
  if (fileContexts && fileContexts.length > 0) {
    const fileContext = fileContexts
      .filter(ctx => ctx.uploadStatus === 'active')
      .map(ctx => `- ${ctx.name}`)
      .join('\n');
    
    if (fileContext) {
      basePrompt += `\n\nReference Documents Available:\n${fileContext}\n\nUse information from these documents to enhance the chart where relevant.`;
    }
  }

  return basePrompt;
}

function getChartEnhancementSystemPrompt(detectedLanguage: SupportedLanguage, enhancementType?: ChartEnhancementType): string {
  const isCustomEnhancement = enhancementType === 'custom_enhance';

  const languageInstructions = isCustomEnhancement
    ? `Original content language: ${getLanguageName(detectedLanguage)}. Follow user instructions for language - if they request a specific language, use it completely.`
    : getLanguageInstructions(detectedLanguage);

  return `You are a Mermaid diagram expert and content enhancer. Your task is to improve chart content while maintaining accuracy and following user instructions precisely.

${languageInstructions}

CRITICAL MERMAID FORMATTING RULES:
- Each Mermaid statement MUST be on a separate line
- Use proper line breaks between all nodes, connections, and styling
- Never put multiple statements on the same line
- Always include proper indentation for subgraphs
- Ensure all brackets, parentheses, and braces are properly formatted

RESPONSE FORMAT:
Return the response in this exact format:
MERMAID_CODE:
[enhanced mermaid code with proper line breaks]

DESCRIPTION:
[enhanced description here]

MERMAID SYNTAX EXAMPLES:
Correct format:
graph LR
    A[Component A]
    B[Component B]
    A --> B

Incorrect format (DO NOT DO THIS):
graph LR A[Component A] B[Component B] A --> B

IMPORTANT RULES:
- Do NOT add phrases like "Here's the enhanced version" or explanations
- Ensure the Mermaid syntax is valid and properly formatted with line breaks
- Each node definition and connection must be on its own line
- Follow the user's instructions exactly, including language changes if requested
- Preserve all factual information and technical accuracy
- Focus on the specific enhancement requested above all else
${isCustomEnhancement ? '- For custom enhancements, prioritize user instructions over language consistency' : '- Maintain language consistency unless explicitly instructed otherwise'}

Detected original language: ${getLanguageName(detectedLanguage)}`;
}

function parseEnhancedChartContent(aiResponse: string): { enhancedMermaidCode: string; enhancedDescription: string } {
  // Look for the structured format
  const mermaidMatch = aiResponse.match(/MERMAID_CODE:\s*([\s\S]*?)(?=DESCRIPTION:|$)/);
  const descriptionMatch = aiResponse.match(/DESCRIPTION:\s*([\s\S]*?)$/);

  let enhancedMermaidCode = '';
  let enhancedDescription = '';

  if (mermaidMatch) {
    enhancedMermaidCode = mermaidMatch[1].trim();

    // Clean up the mermaid code to ensure proper formatting
    enhancedMermaidCode = cleanMermaidCode(enhancedMermaidCode);
  } else {
    // Fallback: try to extract mermaid code from the response
    const lines = aiResponse.split('\n');
    const mermaidLines = lines.filter(line =>
      line.includes('graph') ||
      line.includes('flowchart') ||
      line.includes('-->') ||
      line.includes('---') ||
      line.match(/^\s*[A-Z]\d*\[/) ||
      line.includes('subgraph') ||
      line.includes('end') ||
      line.includes('style')
    );
    enhancedMermaidCode = cleanMermaidCode(mermaidLines.join('\n').trim());
  }

  if (descriptionMatch) {
    enhancedDescription = descriptionMatch[1].trim();
  }

  return { enhancedMermaidCode, enhancedDescription };
}

function cleanMermaidCode(mermaidCode: string): string {
  if (!mermaidCode) return '';

  // Remove any markdown code block markers
  mermaidCode = mermaidCode.replace(/```mermaid\s*/g, '').replace(/```\s*/g, '');

  // Split into lines and clean each line
  const lines = mermaidCode.split('\n');
  const cleanedLines: string[] = [];
  let indentLevel = 0;

  for (let line of lines) {
    line = line.trim();

    // Skip empty lines
    if (!line) continue;

    // Handle subgraph indentation
    if (line.startsWith('subgraph')) {
      cleanedLines.push('    '.repeat(indentLevel) + line);
      indentLevel++;
      continue;
    }

    if (line === 'end') {
      indentLevel = Math.max(0, indentLevel - 1);
      cleanedLines.push('    '.repeat(indentLevel) + line);
      continue;
    }

    // Handle graph declaration
    if (line.startsWith('graph ') || line.startsWith('flowchart ')) {
      cleanedLines.push(line);
      continue;
    }

    // Handle style statements
    if (line.startsWith('style ')) {
      cleanedLines.push('    '.repeat(indentLevel) + line);
      continue;
    }

    // Handle cases where multiple statements might be on one line
    // This is a common issue with AI-generated Mermaid code

    // Pattern 1: Multiple node definitions like "A[Label] B[Label]"
    if (line.includes('[') && line.includes(']')) {
      const nodePattern = /([A-Z]+\d*)\[([^\]]+)\]/g;
      const nodes: string[] = [];
      let match;

      while ((match = nodePattern.exec(line)) !== null) {
        nodes.push(`${match[1]}[${match[2]}]`);
      }

      if (nodes.length > 1) {
        // Multiple nodes found - add each on separate line
        for (const node of nodes) {
          cleanedLines.push('    '.repeat(indentLevel) + node);
        }

        // Look for connections in the same line
        const connectionPattern = /([A-Z]+\d*)\s*-->\s*([A-Z]+\d*)/g;
        let connectionMatch;
        while ((connectionMatch = connectionPattern.exec(line)) !== null) {
          cleanedLines.push('    '.repeat(indentLevel) + `${connectionMatch[1]} --> ${connectionMatch[2]}`);
        }
        continue;
      }
    }

    // Pattern 2: Node definition followed by connection like "A[Label] --> B"
    const nodeConnectionPattern = /^([A-Z]+\d*\[[^\]]+\])\s*-->\s*(.+)$/;
    const nodeConnectionMatch = line.match(nodeConnectionPattern);
    if (nodeConnectionMatch) {
      cleanedLines.push('    '.repeat(indentLevel) + nodeConnectionMatch[1]);
      cleanedLines.push('    '.repeat(indentLevel) + `${nodeConnectionMatch[1].split('[')[0]} --> ${nodeConnectionMatch[2]}`);
      continue;
    }

    // Pattern 3: Multiple connections like "A --> B C --> D"
    if (line.includes('-->')) {
      const connectionParts = line.split(/\s+/);
      let currentConnection = '';

      for (let i = 0; i < connectionParts.length; i++) {
        if (connectionParts[i] === '-->') {
          if (currentConnection && connectionParts[i + 1]) {
            cleanedLines.push('    '.repeat(indentLevel) + `${currentConnection} --> ${connectionParts[i + 1]}`);
            currentConnection = connectionParts[i + 1];
            i++; // Skip the next part as we've used it
          }
        } else if (!currentConnection) {
          currentConnection = connectionParts[i];
        }
      }

      // If we didn't process it as multiple connections, add as is
      if (currentConnection === connectionParts[0]) {
        cleanedLines.push('    '.repeat(indentLevel) + line);
      }
      continue;
    }

    // Add the line with proper indentation
    cleanedLines.push('    '.repeat(indentLevel) + line);
  }

  return cleanedLines.join('\n');
}

function getChartEnhancementExplanation(
  enhancementType: ChartEnhancementType,
  toneType: ChartToneType | undefined,
  detectedLanguage: SupportedLanguage,
  customPrompt?: string
): string {
  const languageName = getLanguageName(detectedLanguage);

  switch (enhancementType) {
    case 'make_shorter':
      return `Chart simplified to focus on essential components in ${languageName}.`;
    case 'make_longer':
      return `Chart expanded with additional relevant details and components in ${languageName}.`;
    case 'change_tone':
      return `Chart terminology adjusted to ${toneType} style in ${languageName} while preserving structure.`;
    case 'custom_enhance':
      const instruction = customPrompt ? ` following the instruction: "${customPrompt.substring(0, 100)}${customPrompt.length > 100 ? '...' : ''}"` : '';
      return `Chart enhanced using custom AI instructions${instruction} in ${languageName}.`;
    default:
      return `Chart enhanced using AI optimization in ${languageName}.`;
  }
}
