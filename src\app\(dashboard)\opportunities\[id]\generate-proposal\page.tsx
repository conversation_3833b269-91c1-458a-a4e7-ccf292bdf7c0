'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Wand2,
  FileText,
  BarChart3,
  Download,
  CheckCircle,
  AlertCircle,
  <PERSON>,
  ArrowRight,
  ArrowLeft,
  Eye,
  RefreshCw,
  Settings,
  Layers,
  Upload,
  Home,
  ChartBarIcon
} from 'lucide-react';
import { FileSelectionDialog } from '@/components/proposals/file-selection-dialog';
import { EnhancedContentSectionSelector } from '@/components/proposals/enhanced-content-section-selector';
import { ProposalSectionType, ProposalChartType } from '@/types/proposal';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { PageLayout } from '@/components/layout/page-layout';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { EmptyState } from '@/components/ui/empty-state';

interface GenerateProposalPageProps {
  params: Promise<{
    id: string;
  }>;
}

interface WizardStep {
  id: string;
  title: string;
  description: string;
  completed: boolean;
}



const CHART_OPTIONS: { value: ProposalChartType; label: string; description: string }[] = [
  { value: 'architecture_high_level', label: 'High-Level Architecture', description: 'System overview diagram' },
  { value: 'architecture_low_level', label: 'Low-Level Architecture', description: 'Detailed component diagram' },
  { value: 'project_timeline', label: 'Project Timeline', description: 'Gantt chart with milestones' },
  { value: 'system_flow', label: 'System Flow', description: 'Data flow and process diagram' },
];

export default function GenerateProposalPage({ params }: GenerateProposalPageProps) {
  const router = useRouter();
  const resolvedParams = use(params);
  const [opportunity, setOpportunity] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState(0);
  const [generating, setGenerating] = useState(false);
  const [fileSelectionOpen, setFileSelectionOpen] = useState(false);
  
  // Form data
  const [title, setTitle] = useState('');
  const [selectedSections, setSelectedSections] = useState<string[]>([
    'executive_summary',
    'scope_of_work',
    'architecture_high_level'
  ]);
  const [sectionCustomPrompts, setSectionCustomPrompts] = useState<Record<string, string>>({});
  const [includeCharts, setIncludeCharts] = useState(true);
  const [selectedCharts, setSelectedCharts] = useState<ProposalChartType[]>([
    'architecture_high_level'
  ]);
  const [selectedFileIds, setSelectedFileIds] = useState<string[]>([]);
  const [customPrompt, setCustomPrompt] = useState('');
  const [locale, setLocale] = useState<'en' | 'ar'>('en');
  
  // Generation state
  const [generationStatus, setGenerationStatus] = useState<'idle' | 'generating' | 'completed' | 'failed'>('idle');
  const [progress, setProgress] = useState(0);
  const [generatedProposalId, setGeneratedProposalId] = useState<string | null>(null);

  // Load opportunity data
  useEffect(() => {
    const loadOpportunity = async () => {
      try {
        setLoading(true);
        console.log('Loading opportunity with ID:', resolvedParams.id);
        const response = await fetch(`/api/opportunities/${resolvedParams.id}`);

        if (response.ok) {
          const result = await response.json();
          console.log('Loaded opportunity:', result);
          setOpportunity(result);
          setTitle(`Proposal for ${result.title}`);
        } else if (response.status === 404) {
          console.error('Opportunity not found - 404');
          toast.error('Opportunity not found');
          router.push('/opportunities');
        } else {
          console.error('Failed to load opportunity - status:', response.status);
          const errorData = await response.json().catch(() => ({}));
          console.error('Error response:', errorData);
          toast.error('Failed to load opportunity');
        }
      } catch (error) {
        console.error('Error loading opportunity:', error);
        toast.error('Failed to load opportunity');
      } finally {
        setLoading(false);
      }
    };

    loadOpportunity();
  }, [resolvedParams.id, router]);

  const steps: WizardStep[] = [
    {
      id: 'basic',
      title: 'Basic Information',
      description: 'Set proposal title and basic configuration',
      completed: currentStep > 0
    },
    {
      id: 'sections',
      title: 'Content Sections',
      description: 'Choose which sections to generate',
      completed: currentStep > 1
    },
    {
      id: 'files',
      title: 'RFP Files',
      description: 'Select files for AI context',
      completed: currentStep > 2
    },
    {
      id: 'generate',
      title: 'Generate',
      description: 'AI proposal generation',
      completed: generationStatus === 'completed'
    }
  ];

  const canProceed = () => {
    switch (currentStep) {
      case 0:
        return title.trim().length > 0;
      case 1:
        return selectedSections.length > 0;
      case 2:
        return selectedFileIds.length > 0;
      default:
        return true;
    }
  };

  const handleNext = () => {
    if (canProceed() && currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleFilesSelected = (fileIds: string[]) => {
    setSelectedFileIds(fileIds);
    setFileSelectionOpen(false);
  };

  const handleSectionSelectionChange = (sections: string[]) => {
    setSelectedSections(sections);
  };

  const handleSectionCustomize = (sectionType: string, customPrompt: string) => {
    setSectionCustomPrompts(prev => ({
      ...prev,
      [sectionType]: customPrompt
    }));
  };

  const handleChartToggle = (chart: ProposalChartType) => {
    setSelectedCharts(prev => 
      prev.includes(chart) 
        ? prev.filter(c => c !== chart)
        : [...prev, chart]
    );
  };

  const handleGenerate = async () => {
    if (!canProceed() || !opportunity) return;

    setGenerating(true);
    setGenerationStatus('generating');
    setProgress(0);

    // Simulate progress updates
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return prev;
        }
        return prev + Math.random() * 15;
      });
    }, 1000);

    try {
      const response = await fetch('/api/proposals/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          opportunityId: opportunity.id,
          title,
          selectedFileIds,
          sections: selectedSections,
          includeCharts,
          chartTypes: includeCharts ? selectedCharts : [],
          customPrompt: customPrompt.trim() || undefined,
          sectionCustomPrompts,
          locale
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to generate proposal');
      }

      clearInterval(progressInterval);
      setGeneratedProposalId(result.data.proposalId);
      setGenerationStatus('completed');
      setProgress(100);
      
      toast.success('Proposal generated successfully!');
      
      // Redirect to the generated proposal after a short delay
      setTimeout(() => {
        router.push(`/proposals/${result.data.proposalId}`);
      }, 2000);

    } catch (error) {
      clearInterval(progressInterval);
      console.error('Error generating proposal:', error);
      setGenerationStatus('failed');
      toast.error(error instanceof Error ? error.message : 'Failed to generate proposal');
    } finally {
      setGenerating(false);
    }
  };

  if (loading) {
    return (
      <PageLayout>
        <div className="flex justify-center items-center h-96">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </PageLayout>
    );
  }

  if (!opportunity) {
    return (
      <PageLayout>
        <Card>
          <CardContent className="p-8">
            <EmptyState
              icon={<ChartBarIcon className="w-12 h-12 text-muted-foreground" />}
              title="Opportunity Not Found"
              description="The opportunity you're looking for doesn't exist or you don't have permission to view it."
              action={{
                label: 'Back to Opportunities',
                onClick: () => router.push('/opportunities'),
                variant: 'primary'
              }}
            />
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  const breadcrumbs = [
    { label: 'Opportunities', href: '/opportunities' },
    { label: opportunity.title, href: `/opportunities/${opportunity.id}` },
    { label: 'Generate Proposal', href: '#' }
  ];

  const actions = (
    <div className="flex items-center gap-2">
      <Button variant="outline" size="sm" onClick={() => router.back()}>
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back
      </Button>
    </div>
  );

  return (
    <PermissionGate
      resource={PermissionResource.PROPOSALS}
      action={PermissionAction.CREATE}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<Wand2 className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to generate proposals."
                action={{
                  label: 'Back to Opportunities',
                  onClick: () => router.push('/opportunities'),
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout
        title="Generate AI Proposal"
        description={`Create a comprehensive proposal for ${opportunity.title}`}
        breadcrumbs={breadcrumbs}
        actions={actions}
      >
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Progress Steps */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Wand2 className="h-5 w-5 text-primary" />
                <CardTitle className="text-xl font-semibold">
                  Generate AI Proposal
                </CardTitle>
              </div>
              <CardDescription>
                Follow these steps to generate a comprehensive proposal
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Step Progress */}
              <div className="relative mb-8">
                <div className="flex items-center justify-between">
                  {steps.map((step, index) => (
                    <div key={step.id} className="flex flex-col items-center relative z-10">
                      {/* Step Circle */}
                      <div className={cn(
                        "w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium border-2",
                        index <= currentStep
                          ? "bg-primary text-primary-foreground border-primary"
                          : "bg-background text-muted-foreground border-border"
                      )}>
                        {step.completed ? (
                          <CheckCircle className="h-5 w-5" />
                        ) : index < currentStep ? (
                          <CheckCircle className="h-5 w-5" />
                        ) : (
                          <span>{index + 1}</span>
                        )}
                      </div>

                      {/* Step Label */}
                      <div className="mt-2 text-center max-w-[100px]">
                        <div className={cn(
                          "text-xs font-medium",
                          index <= currentStep ? "text-foreground" : "text-muted-foreground"
                        )}>
                          {step.title}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Progress Line */}
                <div className="absolute top-5 left-0 right-0 h-0.5 bg-border -z-10">
                  <div
                    className="h-full bg-primary"
                    style={{ width: `${(currentStep / (steps.length - 1)) * 100}%` }}
                  />
                </div>
              </div>

              {/* Current Step Info */}
              <div className="text-center space-y-2 p-4 bg-muted/50 rounded-lg border">
                <h3 className="text-lg font-semibold">
                  {steps[currentStep].title}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {steps[currentStep].description}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Step Content */}
          <Card>
            <CardContent className="p-6">
              {currentStep === 0 && (
                <div className="space-y-6">
                  <div className="space-y-4">
                    {/* Proposal Title */}
                    <div className="space-y-2">
                      <Label htmlFor="title">
                        Proposal Title *
                      </Label>
                      <Input
                        id="title"
                        value={title}
                        onChange={(e) => setTitle(e.target.value)}
                        placeholder="Enter a descriptive title for your proposal"
                      />
                      <p className="text-sm text-muted-foreground">
                        This will be the main title of your generated proposal
                      </p>
                    </div>

                    {/* Language Selection */}
                    <div className="space-y-2">
                      <Label htmlFor="locale">
                        Content Language
                      </Label>
                      <Select value={locale} onValueChange={(value: 'en' | 'ar') => setLocale(value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select content language" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="en">English</SelectItem>
                          <SelectItem value="ar">العربية (Arabic)</SelectItem>
                        </SelectContent>
                      </Select>
                      <p className="text-sm text-muted-foreground">
                        Choose the language for AI-generated proposal content
                      </p>
                    </div>

                    {/* Chart Options */}
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="includeCharts"
                          checked={includeCharts}
                          onCheckedChange={(checked) => setIncludeCharts(checked as boolean)}
                        />
                        <Label htmlFor="includeCharts" className="cursor-pointer">
                          Include AI-generated charts and diagrams
                        </Label>
                      </div>
                      <p className="text-sm text-muted-foreground ml-6">
                        Generate Mermaid diagrams for architecture, timelines, and system flows
                      </p>
                    </div>

                    {/* Custom Instructions */}
                    <div className="space-y-2">
                      <Label htmlFor="customPrompt">
                        Custom Instructions <span className="text-muted-foreground">(Optional)</span>
                      </Label>
                      <Textarea
                        id="customPrompt"
                        value={customPrompt}
                        onChange={(e) => setCustomPrompt(e.target.value)}
                        placeholder={locale === 'ar'
                          ? "قدم تعليمات أو متطلبات محددة للذكاء الاصطناعي..."
                          : "Provide specific instructions or requirements for the AI..."
                        }
                        rows={4}
                        className="resize-none"
                        dir={locale === 'ar' ? 'rtl' : 'ltr'}
                      />
                      <p className="text-sm text-muted-foreground">
                        Provide additional context or specific requirements for the proposal generation
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {currentStep === 1 && (
                <div className="space-y-6">
                  <EnhancedContentSectionSelector
                    selectedSections={selectedSections}
                    onSelectionChange={handleSectionSelectionChange}
                    onSectionCustomize={handleSectionCustomize}
                    customPrompts={sectionCustomPrompts}
                  />

                    {includeCharts && (
                      <>
                        <div className="border-t pt-4">
                          <div className="space-y-2 mb-4">
                            <h4 className="font-medium">Chart Types</h4>
                            <p className="text-sm text-muted-foreground">Select which types of diagrams to generate</p>
                          </div>
                          <div className="grid grid-cols-1 gap-3">
                            {CHART_OPTIONS.map((chart) => (
                              <Card
                                key={chart.value}
                                className={cn(
                                  "cursor-pointer hover:shadow-sm",
                                  selectedCharts.includes(chart.value)
                                    ? "ring-2 ring-primary bg-primary/5 border-primary/20"
                                    : "hover:bg-muted/50"
                                )}
                                onClick={() => handleChartToggle(chart.value)}
                              >
                                <CardContent className="p-3">
                                  <div className="flex items-center space-x-3">
                                    <Checkbox
                                      checked={selectedCharts.includes(chart.value)}
                                      onCheckedChange={(checked) => handleChartToggle(chart.value)}
                                    />
                                    <div className="flex-1">
                                      <h5 className="text-sm font-medium">{chart.label}</h5>
                                      <p className="text-xs text-muted-foreground">{chart.description}</p>
                                    </div>
                                  </div>
                                </CardContent>
                              </Card>
                            ))}
                          </div>
                        </div>
                      </>
                    )}

                    {selectedSections.length === 0 && (
                      <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>
                          Please select at least one section to generate.
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                </div>
              )}

              {currentStep === 2 && (
                <div className="space-y-6">
                  <div className="space-y-2">
                    <h3 className="text-lg font-semibold">Select RFP Files</h3>
                    <p className="text-sm text-muted-foreground">Choose documents for AI context analysis</p>
                  </div>

                  <div className="space-y-4">
                    <div className="text-center p-6 border border-dashed rounded-lg">
                      <Button
                        onClick={() => setFileSelectionOpen(true)}
                        size="lg"
                      >
                        <FileText className="h-4 w-4 mr-2" />
                        Select Files ({selectedFileIds.length} selected)
                      </Button>
                      <p className="text-sm text-muted-foreground mt-2">
                        Choose RFP documents to provide context for AI analysis
                      </p>
                    </div>

                    {selectedFileIds.length > 0 && (
                      <Alert>
                        <CheckCircle className="h-4 w-4" />
                        <AlertDescription>
                          <strong>{selectedFileIds.length}</strong> file{selectedFileIds.length !== 1 ? 's' : ''} selected for AI analysis.
                        </AlertDescription>
                      </Alert>
                    )}

                    {selectedFileIds.length === 0 && (
                      <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>
                          Please select at least one file to provide context for AI generation.
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                </div>
              )}

              {currentStep === 3 && (
                <div className="space-y-6">
                  <div className="space-y-2">
                    <h3 className="text-lg font-semibold">Generate Proposal</h3>
                    <p className="text-sm text-muted-foreground">
                      {generationStatus === 'idle' && 'Ready to generate your AI-powered proposal'}
                      {generationStatus === 'generating' && 'AI is generating your proposal...'}
                      {generationStatus === 'completed' && 'Proposal generated successfully!'}
                      {generationStatus === 'failed' && 'Generation failed. Please try again.'}
                    </p>
                  </div>

                  <div className="space-y-4">
                    {/* Generation Summary */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                          <CheckCircle className="h-5 w-5 text-primary" />
                          <span>Generation Summary</span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          <div className="space-y-1">
                            <div className="font-medium">Proposal Title</div>
                            <div className="text-sm text-muted-foreground">{title}</div>
                          </div>
                          <div className="space-y-1">
                            <div className="font-medium">Sections</div>
                            <div className="text-sm text-muted-foreground">{selectedSections.length} selected</div>
                          </div>
                          <div className="space-y-1">
                            <div className="font-medium">Customized</div>
                            <div className="text-sm text-muted-foreground">
                              {Object.keys(sectionCustomPrompts).filter(key => sectionCustomPrompts[key].trim()).length} sections
                            </div>
                          </div>
                          <div className="space-y-1">
                            <div className="font-medium">Files</div>
                            <div className="text-sm text-muted-foreground">{selectedFileIds.length} selected</div>
                          </div>
                          <div className="space-y-1">
                            <div className="font-medium">Charts</div>
                            <div className="text-sm text-muted-foreground">
                              {includeCharts ? `${selectedCharts.length} types` : 'Disabled'}
                            </div>
                          </div>
                          <div className="space-y-1">
                            <div className="font-medium">Language</div>
                            <div className="text-sm text-muted-foreground">
                              {locale === 'en' ? 'English' : 'العربية'}
                            </div>
                          </div>
                        </div>

                        {customPrompt && (
                          <div className="space-y-2">
                            <div className="font-medium">Custom Instructions</div>
                            <div className="text-sm text-muted-foreground p-3 bg-muted rounded-lg">
                              {customPrompt}
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* Generation Progress */}
                    {generationStatus === 'generating' && (
                      <Card>
                        <CardContent className="p-6">
                          <div className="space-y-4">
                            <div className="flex items-center space-x-2">
                              <RefreshCw className="h-4 w-4 animate-spin text-primary" />
                              <span className="text-sm font-medium">Generating proposal...</span>
                            </div>
                            <Progress value={progress} className="w-full" />
                            <p className="text-xs text-muted-foreground">
                              AI is analyzing your RFP files and generating comprehensive proposal content.
                              This may take a few minutes.
                            </p>
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {/* Success State */}
                    {generationStatus === 'completed' && generatedProposalId && (
                      <Alert>
                        <CheckCircle className="h-4 w-4" />
                        <AlertDescription>
                          <div className="space-y-3">
                            <div>
                              <div className="font-medium">Proposal Generated Successfully!</div>
                              <div className="text-sm">Your AI-powered proposal has been created and is ready for review.</div>
                            </div>
                            <div className="flex space-x-2">
                              <Button
                                onClick={() => router.push(`/proposals/${generatedProposalId}`)}
                                size="sm"
                              >
                                <Eye className="h-4 w-4 mr-2" />
                                View Proposal
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => router.push(`/opportunities/${opportunity.id}`)}
                              >
                                Back to Opportunity
                              </Button>
                            </div>
                          </div>
                        </AlertDescription>
                      </Alert>
                    )}

                    {/* Error State */}
                    {generationStatus === 'failed' && (
                      <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>
                          <div className="space-y-3">
                            <div>
                              <div className="font-medium">Generation Failed</div>
                              <div className="text-sm">There was an error generating your proposal. Please try again.</div>
                            </div>
                            <Button
                              onClick={() => {
                                setGenerationStatus('idle');
                                setProgress(0);
                              }}
                              variant="outline"
                              size="sm"
                            >
                              <RefreshCw className="h-4 w-4 mr-2" />
                              Try Again
                            </Button>
                          </div>
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Navigation Controls */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  {currentStep > 0 && (
                    <Button
                      variant="outline"
                      onClick={handlePrevious}
                      disabled={generating}
                    >
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      Previous
                    </Button>
                  )}
                </div>

                <div className="text-sm text-muted-foreground">
                  Step {currentStep + 1} of {steps.length}
                </div>

                <div>
                  {currentStep < steps.length - 1 ? (
                    <Button
                      onClick={handleNext}
                      disabled={!canProceed() || generating}
                    >
                      Next
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  ) : (
                    <Button
                      onClick={handleGenerate}
                      disabled={!canProceed() || generating || generationStatus === 'completed'}
                    >
                      {generating ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <Wand2 className="h-4 w-4 mr-2" />
                          Generate Proposal
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* File Selection Dialog */}
        <FileSelectionDialog
          open={fileSelectionOpen}
          onOpenChange={setFileSelectionOpen}
          opportunityId={opportunity.id}
          onFilesSelected={handleFilesSelected}
        />
      </PageLayout>
    </PermissionGate>
  );
}
