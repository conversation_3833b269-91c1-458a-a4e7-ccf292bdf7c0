'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { SearchableSelect, SearchableSelectOption } from '@/components/ui/searchable-select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  CalendarIcon,
  Clock,
  Users,
  Building2,
  User,
  AlertCircle,
  CheckCircle,
  Loader2,
  AlertTriangle,
  ExternalLink
} from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import Link from 'next/link';

// Types
interface Opportunity {
  id: string;
  title: string;
  value?: number;
  stage: string;
  contact?: {
    firstName: string;
    lastName: string;
    email?: string;
  };
  company?: {
    name: string;
  };
}

interface User {
  id: string;
  firstName?: string;
  lastName?: string;
  email: string;
  avatarUrl?: string;
  status: string;
  tenantUser?: {
    id: string;
    role: string;
    status: string;
    joinedAt: Date;
    teamId?: string;
    department?: string;
    jobTitle?: string;
    team?: {
      id: string;
      name: string;
      color?: string;
    };
  };
}

interface Team {
  id: string;
  name: string;
  description?: string;
}

interface HandoverTemplate {
  id: string;
  name: string;
  description?: string;
  category: string;
  estimatedDuration?: number;
}

interface ExistingHandover {
  id: string;
  title: string;
  status: string;
  priority: string;
  createdAt: string;
  salesRep?: {
    firstName?: string;
    lastName?: string;
  };
  deliveryManager?: {
    firstName?: string;
    lastName?: string;
  };
}

interface HandoverFormData {
  opportunityId: string;
  templateId?: string;
  title: string;
  description?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  salesRepId?: string;
  deliveryManagerId?: string;
  assignedTeamIds: string[];
  scheduledDate?: string;
  estimatedDuration?: number;
}

interface HandoverFormProps {
  opportunityId?: string;
  handoverId?: string;
  onSuccess?: (handover: any) => void;
  onCancel?: () => void;
}

export default function HandoverForm({ 
  opportunityId, 
  handoverId, 
  onSuccess, 
  onCancel 
}: HandoverFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [templates, setTemplates] = useState<HandoverTemplate[]>([]);
  const [selectedOpportunity, setSelectedOpportunity] = useState<Opportunity | null>(null);
  const [existingHandover, setExistingHandover] = useState<ExistingHandover | null>(null);
  const [checkingExistingHandover, setCheckingExistingHandover] = useState(false);

  const [formData, setFormData] = useState<HandoverFormData>({
    opportunityId: opportunityId || '',
    templateId: '',
    title: '',
    description: '',
    priority: 'medium',
    salesRepId: '',
    deliveryManagerId: '',
    assignedTeamIds: [],
    scheduledDate: '',
    estimatedDuration: undefined,
  });

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setDataLoading(true);
        setError(null);

        const [oppsRes, usersRes, teamsRes, templatesRes] = await Promise.all([
          fetch('/api/opportunities?stage=Closed Won&limit=50'),
          fetch('/api/users?status=active&limit=100'),
          fetch('/api/teams'),
          fetch('/api/handover-templates')
        ]);

        if (oppsRes.ok) {
          const oppsData = await oppsRes.json();
          setOpportunities(oppsData.opportunities || []);

          // If opportunityId is provided, find and set the opportunity
          if (opportunityId) {
            const opportunity = oppsData.opportunities?.find((opp: Opportunity) => opp.id === opportunityId);
            if (opportunity) {
              setSelectedOpportunity(opportunity);
              setFormData(prev => ({
                ...prev,
                title: `${opportunity.title} - Handover`,
                description: `Sales-to-delivery handover for ${opportunity.title}`
              }));
            }
          }
        }

        if (usersRes.ok) {
          const usersData = await usersRes.json();
          setUsers(usersData.data || []);
        } else {
          console.error('Failed to fetch users:', await usersRes.text());
        }

        if (teamsRes.ok) {
          const teamsData = await teamsRes.json();
          setTeams(teamsData.teams || []);
        }

        if (templatesRes.ok) {
          const templatesData = await templatesRes.json();
          setTemplates(templatesData.templates || []);
        }
      } catch (err) {
        console.error('Error fetching form data:', err);
        setError('Failed to load form data. Please refresh the page.');
      } finally {
        setDataLoading(false);
      }
    };

    fetchData();
  }, [opportunityId]);

  // Fetch existing handover data for editing
  useEffect(() => {
    const fetchHandoverData = async () => {
      if (!handoverId) return;

      try {
        setError(null);

        const response = await fetch(`/api/handovers/${handoverId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch handover data');
        }

        const handover = await response.json();

        // Populate form with existing handover data
        setFormData({
          opportunityId: handover.opportunityId || '',
          templateId: handover.templateId || '',
          title: handover.title || '',
          description: handover.description || '',
          priority: handover.priority || 'medium',
          salesRepId: handover.salesRepId || '',
          deliveryManagerId: handover.deliveryManagerId || '',
          assignedTeamIds: handover.assignedTeamIds || [],
          scheduledDate: handover.scheduledDate ?
            new Date(handover.scheduledDate).toISOString().slice(0, 16) : '',
          estimatedDuration: handover.estimatedDuration || undefined,
        });

        // Set selected opportunity if available
        if (handover.opportunity) {
          setSelectedOpportunity(handover.opportunity);
        }

      } catch (err) {
        console.error('Error fetching handover data:', err);
        setError('Failed to load handover data. Please refresh the page.');
      }
    };

    // Only fetch handover data after initial data is loaded
    if (!dataLoading && handoverId) {
      fetchHandoverData();
    }
  }, [handoverId, dataLoading]);

  // Set selected opportunity when opportunities are loaded and we have a handover with opportunityId
  useEffect(() => {
    if (handoverId && opportunities.length > 0 && formData.opportunityId && !selectedOpportunity) {
      const opportunity = opportunities.find(opp => opp.id === formData.opportunityId);
      if (opportunity) {
        setSelectedOpportunity(opportunity);
      }
    }
  }, [opportunities, formData.opportunityId, handoverId, selectedOpportunity]);

  // Check for existing handover when component loads with opportunityId
  useEffect(() => {
    if (opportunityId && !handoverId && !dataLoading) {
      checkExistingHandover(opportunityId);
    }
  }, [opportunityId, handoverId, dataLoading]);

  // Check for existing handover when opportunity is selected
  const checkExistingHandover = async (opportunityId: string) => {
    if (!opportunityId || handoverId) {
      setExistingHandover(null);
      return;
    }

    try {
      setCheckingExistingHandover(true);
      const response = await fetch(`/api/handovers?opportunityId=${opportunityId}&limit=1`);
      if (response.ok) {
        const data = await response.json();
        if (data.handovers && data.handovers.length > 0) {
          setExistingHandover(data.handovers[0]);
        } else {
          setExistingHandover(null);
        }
      }
    } catch (error) {
      console.error('Error checking existing handover:', error);
      // Don't show error toast for this check as it's not critical
    } finally {
      setCheckingExistingHandover(false);
    }
  };

  // Handle opportunity selection
  const handleOpportunityChange = (oppId: string) => {
    const opportunity = opportunities.find(opp => opp.id === oppId);
    setSelectedOpportunity(opportunity || null);
    setFormData(prev => ({
      ...prev,
      opportunityId: oppId,
      title: opportunity ? `${opportunity.title} - Handover` : '',
      description: opportunity ? `Sales-to-delivery handover for ${opportunity.title}` : ''
    }));

    // Check for existing handover
    if (oppId) {
      checkExistingHandover(oppId);
    } else {
      setExistingHandover(null);
    }
  };

  // Handle template selection
  const handleTemplateChange = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    setFormData(prev => ({
      ...prev,
      templateId,
      estimatedDuration: template?.estimatedDuration || prev.estimatedDuration
    }));
  };

  // Handle team selection
  const handleTeamToggle = (teamId: string) => {
    setFormData(prev => ({
      ...prev,
      assignedTeamIds: prev.assignedTeamIds.includes(teamId)
        ? prev.assignedTeamIds.filter(id => id !== teamId)
        : [...prev.assignedTeamIds, teamId]
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const submitData = {
        ...formData,
        scheduledDate: formData.scheduledDate ? new Date(formData.scheduledDate).toISOString() : undefined,
        templateId: formData.templateId || undefined,
        salesRepId: formData.salesRepId || undefined,
        deliveryManagerId: formData.deliveryManagerId || undefined,
        estimatedDuration: formData.estimatedDuration || undefined,
      };

      const url = handoverId ? `/api/handovers/${handoverId}` : '/api/handovers';
      const method = handoverId ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${handoverId ? 'update' : 'create'} handover`);
      }

      const handover = await response.json();

      if (onSuccess) {
        onSuccess(handover);
      } else {
        router.push(`/handovers/${handover.id}`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value?: number) => {
    if (!value) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(value);
  };

  const getUserDisplayName = (user: User) => {
    const name = user.firstName && user.lastName
      ? `${user.firstName} ${user.lastName}`
      : user.email;

    // Add role and team info if available
    const roleInfo = user.tenantUser?.role ? ` (${user.tenantUser.role})` : '';
    const teamInfo = user.tenantUser?.team?.name ? ` - ${user.tenantUser.team.name}` : '';

    return `${name}${roleInfo}${teamInfo}`;
  };

  const getUserShortName = (user: User) => {
    return user.firstName && user.lastName
      ? `${user.firstName} ${user.lastName}`
      : user.email;
  };

  // Filter users by role for better suggestions
  const getSalesUsers = () => {
    return users.filter(user =>
      user.status === 'active' &&
      (user.tenantUser?.role?.toLowerCase().includes('sales') ||
       user.tenantUser?.role?.toLowerCase().includes('account') ||
       user.tenantUser?.jobTitle?.toLowerCase().includes('sales'))
    );
  };

  const getDeliveryUsers = () => {
    return users.filter(user =>
      user.status === 'active' &&
      (user.tenantUser?.role?.toLowerCase().includes('delivery') ||
       user.tenantUser?.role?.toLowerCase().includes('project') ||
       user.tenantUser?.role?.toLowerCase().includes('manager') ||
       user.tenantUser?.jobTitle?.toLowerCase().includes('delivery') ||
       user.tenantUser?.jobTitle?.toLowerCase().includes('project'))
    );
  };

  const getActiveUsers = () => {
    return users.filter(user => user.status === 'active');
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {dataLoading ? (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <div className="animate-pulse">
                <div className="h-6 bg-gray-200 rounded w-1/3 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="animate-pulse space-y-4">
                <div className="h-10 bg-gray-200 rounded"></div>
                <div className="h-20 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <div className="animate-pulse">
                <div className="h-6 bg-gray-200 rounded w-1/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="animate-pulse space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="h-10 bg-gray-200 rounded"></div>
                  <div className="h-10 bg-gray-200 rounded"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
        {/* Opportunity Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="w-5 h-5" />
              Opportunity Details
            </CardTitle>
            <CardDescription>
              Select the opportunity for this handover
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="opportunity">Opportunity *</Label>
              <div className="mt-2">
                <SearchableSelect
                  value={formData.opportunityId}
                  onValueChange={handleOpportunityChange}
                  disabled={!!opportunityId}
                  placeholder="Select an opportunity"
                  searchPlaceholder="Search opportunities..."
                  options={opportunities.map((opportunity) => ({
                    value: opportunity.id,
                    label: opportunity.title,
                    description: `${opportunity.company?.name ||
                      `${opportunity.contact?.firstName} ${opportunity.contact?.lastName}`}${
                      opportunity.value ? ` • ${formatCurrency(opportunity.value)}` : ''
                    }`
                  }))}
                />
              </div>
            </div>

            {/* Show existing handover warning */}
            {checkingExistingHandover && (
              <Alert>
                <Loader2 className="h-4 w-4 animate-spin" />
                <AlertDescription>
                  Checking for existing handovers...
                </AlertDescription>
              </Alert>
            )}

            {existingHandover && !checkingExistingHandover && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-2">
                    <p>
                      <strong>Handover already exists for this opportunity!</strong>
                    </p>
                    <p className="text-sm">
                      Handover "{existingHandover.title}" (Status: {existingHandover.status}, Priority: {existingHandover.priority}) was created on{' '}
                      {new Date(existingHandover.createdAt).toLocaleDateString()}.
                    </p>
                    <div className="flex items-center gap-2 mt-2">
                      <Link
                        href={`/handovers/${existingHandover.id}`}
                        className="inline-flex items-center gap-1 text-sm font-medium text-blue-600 hover:text-blue-800 underline"
                      >
                        View existing handover
                        <ExternalLink className="h-3 w-3" />
                      </Link>
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">
                      You cannot create multiple handovers for the same opportunity.
                    </p>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {selectedOpportunity && (
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Client:</span>{' '}
                    {selectedOpportunity.company?.name ||
                     `${selectedOpportunity.contact?.firstName} ${selectedOpportunity.contact?.lastName}`}
                  </div>
                  <div>
                    <span className="font-medium">Value:</span>{' '}
                    {formatCurrency(selectedOpportunity.value)}
                  </div>
                  <div>
                    <span className="font-medium">Stage:</span>{' '}
                    <Badge variant="outline">{selectedOpportunity.stage}</Badge>
                  </div>
                  {selectedOpportunity.contact?.email && (
                    <div>
                      <span className="font-medium">Contact:</span>{' '}
                      {selectedOpportunity.contact.email}
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Handover Details */}
        <Card>
          <CardHeader>
            <CardTitle>Handover Details</CardTitle>
            <CardDescription>
              Configure the handover process details
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="title">Handover Title *</Label>
              <Input
                id="title"
                className="mt-2"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Enter handover title"
                required
              />
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                className="mt-2"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe the handover requirements and context"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="priority">Priority</Label>
                <div className="mt-2">
                  <SearchableSelect
                    value={formData.priority}
                    onValueChange={(value: any) => setFormData(prev => ({ ...prev, priority: value }))}
                    placeholder="Select priority"
                    options={[
                      { value: 'low', label: 'Low' },
                      { value: 'medium', label: 'Medium' },
                      { value: 'high', label: 'High' },
                      { value: 'urgent', label: 'Urgent' }
                    ]}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="template">Checklist Template</Label>
                <div className="mt-2">
                  <SearchableSelect
                    value={formData.templateId || ''}
                    onValueChange={handleTemplateChange}
                    placeholder="Select a template"
                    searchPlaceholder="Search templates..."
                    allowClear
                    options={templates.map((template) => ({
                      value: template.id,
                      label: template.name,
                      description: `${template.category}${
                        template.estimatedDuration ? ` • ${template.estimatedDuration}h` : ''
                      }`
                    }))}
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="scheduledDate">Scheduled Date</Label>
                <Input
                  id="scheduledDate"
                  className="mt-2"
                  type="datetime-local"
                  value={formData.scheduledDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, scheduledDate: e.target.value }))}
                />
              </div>

              <div>
                <Label htmlFor="estimatedDuration">Estimated Duration (hours)</Label>
                <Input
                  id="estimatedDuration"
                  className="mt-2"
                  type="number"
                  min="1"
                  value={formData.estimatedDuration || ''}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    estimatedDuration: e.target.value ? parseInt(e.target.value) : undefined
                  }))}
                  placeholder="Enter estimated hours"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Team Assignment */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              Team Assignment
            </CardTitle>
            <CardDescription>
              Assign team members to this handover
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="salesRep">Sales Representative</Label>
                <div className="mt-2">
                  <SearchableSelect
                    value={formData.salesRepId || ''}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, salesRepId: value }))}
                    placeholder="Select sales rep"
                    searchPlaceholder="Search sales representatives..."
                    allowClear
                    options={[
                      // Suggested Sales Users First
                      ...getSalesUsers().map((user) => ({
                        value: user.id,
                        label: getUserShortName(user),
                        description: `${user.tenantUser?.role || ''}${
                          user.tenantUser?.team?.name ? ` • ${user.tenantUser.team.name}` : ''
                        }${user.tenantUser?.jobTitle ? ` • ${user.tenantUser.jobTitle}` : ''}`
                      })),
                      // All other active users
                      ...getActiveUsers()
                        .filter(user => !getSalesUsers().some(su => su.id === user.id))
                        .map((user) => ({
                          value: user.id,
                          label: getUserShortName(user),
                          description: `${user.tenantUser?.role || ''}${
                            user.tenantUser?.team?.name ? ` • ${user.tenantUser.team.name}` : ''
                          }${user.tenantUser?.jobTitle ? ` • ${user.tenantUser.jobTitle}` : ''}`
                        }))
                    ]}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="deliveryManager">Delivery Manager</Label>
                <div className="mt-2">
                  <SearchableSelect
                    value={formData.deliveryManagerId || ''}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, deliveryManagerId: value }))}
                    placeholder="Select delivery manager"
                    searchPlaceholder="Search delivery managers..."
                    allowClear
                    options={[
                      // Suggested Delivery Users First
                      ...getDeliveryUsers().map((user) => ({
                        value: user.id,
                        label: getUserShortName(user),
                        description: `${user.tenantUser?.role || ''}${
                          user.tenantUser?.team?.name ? ` • ${user.tenantUser.team.name}` : ''
                        }${user.tenantUser?.jobTitle ? ` • ${user.tenantUser.jobTitle}` : ''}`
                      })),
                      // All other active users
                      ...getActiveUsers()
                        .filter(user => !getDeliveryUsers().some(du => du.id === user.id))
                        .map((user) => ({
                          value: user.id,
                          label: getUserShortName(user),
                          description: `${user.tenantUser?.role || ''}${
                            user.tenantUser?.team?.name ? ` • ${user.tenantUser.team.name}` : ''
                          }${user.tenantUser?.jobTitle ? ` • ${user.tenantUser.jobTitle}` : ''}`
                        }))
                    ]}
                  />
                </div>
              </div>
            </div>

            <div>
              <Label>Assigned Teams</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {teams.map((team) => (
                  <div
                    key={team.id}
                    className={cn(
                      "p-3 border rounded-lg cursor-pointer transition-colors",
                      formData.assignedTeamIds.includes(team.id)
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-200 hover:border-gray-300"
                    )}
                    onClick={() => handleTeamToggle(team.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">{team.name}</div>
                        {team.description && (
                          <div className="text-sm text-gray-500">{team.description}</div>
                        )}
                      </div>
                      {formData.assignedTeamIds.includes(team.id) && (
                        <CheckCircle className="w-5 h-5 text-blue-500" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex justify-end gap-4">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button
            type="submit"
            disabled={loading || !formData.opportunityId || !formData.title || (existingHandover && !handoverId)}
          >
            {loading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
            {handoverId ? 'Update Handover' : 'Create Handover'}
          </Button>
        </div>
      </form>
      )}
    </div>
  );
}
