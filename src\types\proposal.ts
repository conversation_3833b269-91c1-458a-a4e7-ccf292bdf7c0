import { Proposal, ProposalSection, ProposalChart, Opportunity, User, Tenant } from '@prisma/client';

// ============================================================================
// PROPOSAL TYPES
// ============================================================================

export type ProposalStatus = 'draft' | 'generating' | 'generated' | 'reviewed' | 'approved' | 'sent' | 'accepted' | 'rejected';
export type GenerationStatus = 'pending' | 'generating' | 'completed' | 'failed';

export type ProposalSectionType = 
  | 'executive_summary'
  | 'scope_of_work'
  | 'architecture_high_level'
  | 'architecture_low_level'
  | 'out_of_scope'
  | 'assumptions'
  | 'project_plan'
  | 'resource_estimation';

export type ProposalChartType = 
  | 'architecture_high_level'
  | 'architecture_low_level'
  | 'project_timeline'
  | 'system_flow';

export type SectionStatus = 'draft' | 'generated' | 'reviewed' | 'approved';
export type ChartStatus = 'draft' | 'generated' | 'rendered' | 'approved';

// ============================================================================
// EXTENDED TYPES WITH RELATIONS
// ============================================================================

export interface ProposalWithRelations extends Proposal {
  tenant: Tenant;
  opportunity?: Opportunity | null;
  createdBy?: User | null;
  sections: ProposalSectionWithRelations[];
  charts: ProposalChartWithRelations[];
}

export interface ProposalSectionWithRelations extends ProposalSection {
  tenant: Tenant;
  proposal: Proposal;
  createdBy?: User | null;
}

export interface ProposalChartWithRelations extends ProposalChart {
  tenant: Tenant;
  proposal: Proposal;
  createdBy?: User | null;
}

// ============================================================================
// AI GENERATION TYPES
// ============================================================================

export interface FileSelectionData {
  documentId: string;
  name: string;
  filePath: string;
  mimeType: string;
  fileSize: number;
  isSelected: boolean;
  geminiFileUri?: string;
}

export interface ProposalGenerationRequest {
  opportunityId: string;
  title: string;
  selectedFileIds: string[];
  sections: ProposalSectionType[];
  includeCharts: boolean;
  chartTypes?: ProposalChartType[];
  customPrompt?: string;
  locale?: 'en' | 'ar';
}

export interface ProposalGenerationResponse {
  proposalId: string;
  status: GenerationStatus;
  message: string;
  estimatedCompletionTime?: number; // in minutes
}

export interface SectionGenerationRequest {
  proposalId: string;
  sectionType: ProposalSectionType;
  customPrompt?: string;
  regenerate?: boolean;
}

export interface SectionGenerationResponse {
  sectionId: string;
  content: string;
  wordCount: number;
  tokensUsed: number;
  confidence: number;
}

export interface SectionReorderRequest {
  sectionOrders: Array<{
    id: string;
    orderIndex: number;
  }>;
}

export interface SectionReorderResponse {
  message: string;
  sections: ProposalSectionWithRelations[];
}

export interface ChartGenerationRequest {
  proposalId: string;
  chartType: ProposalChartType;
  title: string;
  description?: string;
  customPrompt?: string;
}

export interface ChartGenerationResponse {
  chartId: string;
  mermaidCode: string;
  renderedImagePath?: string;
  title: string;
  description?: string;
}

// ============================================================================
// GEMINI FILE MANAGEMENT
// ============================================================================

export interface GeminiFileUploadRequest {
  documentId: string;
  filePath: string;
  mimeType: string;
  displayName: string;
  tenantId: string;
}

export interface GeminiFileUploadResponse {
  fileUri: string;
  name: string;
  displayName: string;
  mimeType: string;
  sizeBytes: string;
  createTime: string;
  updateTime: string;
  expirationTime: string;
  sha256Hash: string;
  state: 'PROCESSING' | 'ACTIVE' | 'FAILED';
  error?: string;
}

export interface GeminiFileContext {
  fileUri: string;
  documentId: string;
  name: string;
  mimeType?: string;
  fileSize?: number;
  uploadStatus: 'pending' | 'uploading' | 'active' | 'failed';
  error?: string;
  relevantSections?: string[];
}

export interface GeminiFileUploadBatchRequest {
  documentIds: string[];
  tenantId: string;
}

export interface GeminiFileUploadBatchResponse {
  successful: GeminiFileUploadResponse[];
  failed: Array<{
    documentId: string;
    error: string;
  }>;
  totalProcessed: number;
  successCount: number;
  failureCount: number;
}

// ============================================================================
// DOCUMENT GENERATION
// ============================================================================

export interface DocumentGenerationRequest {
  proposalId: string;
  format: 'docx' | 'pdf';
  includeCharts: boolean;
  includeBranding: boolean;
  customStyling?: DocumentStyling;
}

export interface DocumentStyling {
  // Legacy options for backward compatibility
  fontFamily?: string;
  fontSize?: number;
  headerColor?: string;
  accentColor?: string;
  logoUrl?: string;
  companyName?: string;

  // Enhanced options for md-to-docx library
  documentType?: 'document' | 'report';
  titleSize?: number;
  headingSpacing?: number;
  paragraphSpacing?: number;
  lineSpacing?: number;
  heading1Size?: number;
  heading2Size?: number;
  heading3Size?: number;
  heading4Size?: number;
  heading5Size?: number;
  paragraphSize?: number;
  listItemSize?: number;
  codeBlockSize?: number;
  blockquoteSize?: number;
  tocFontSize?: number;
  paragraphAlignment?: 'LEFT' | 'RIGHT' | 'CENTER' | 'JUSTIFIED';
  headingAlignment?: 'LEFT' | 'RIGHT' | 'CENTER' | 'JUSTIFIED';
  heading1Alignment?: 'LEFT' | 'RIGHT' | 'CENTER' | 'JUSTIFIED';
  heading2Alignment?: 'LEFT' | 'RIGHT' | 'CENTER' | 'JUSTIFIED';
  heading3Alignment?: 'LEFT' | 'RIGHT' | 'CENTER' | 'JUSTIFIED';
  heading4Alignment?: 'LEFT' | 'RIGHT' | 'CENTER' | 'JUSTIFIED';
  heading5Alignment?: 'LEFT' | 'RIGHT' | 'CENTER' | 'JUSTIFIED';
  blockquoteAlignment?: 'LEFT' | 'RIGHT' | 'CENTER' | 'JUSTIFIED';
}

export interface DocumentGenerationResponse {
  documentPath: string;
  fileName: string;
  fileSize: number;
  downloadUrl: string;
}

// ============================================================================
// MERMAID CHART TYPES
// ============================================================================

export interface MermaidChartData {
  code: string;
  title: string;
  description?: string;
  type: ProposalChartType;
}

export interface MermaidRenderRequest {
  mermaidCode: string;
  title: string;
  format: 'png' | 'svg';
  theme?: 'default' | 'dark' | 'forest' | 'neutral';
  width?: number;
  height?: number;
}

export interface MermaidRenderResponse {
  imagePath: string;
  imageUrl: string;
  width: number;
  height: number;
  format: string;
}

// ============================================================================
// PROPOSAL ANALYTICS
// ============================================================================

export interface ProposalAnalytics {
  totalProposals: number;
  aiGeneratedProposals: number;
  averageGenerationTime: number; // in minutes
  successRate: number; // percentage
  mostUsedSections: ProposalSectionType[];
  averageWordCount: number;
  totalTokensUsed: number;
  estimatedCostSavings: number; // in hours
}

export interface SectionAnalytics {
  sectionType: ProposalSectionType;
  averageWordCount: number;
  averageTokensUsed: number;
  averageGenerationTime: number;
  successRate: number;
  regenerationRate: number;
}

// ============================================================================
// VALIDATION SCHEMAS
// ============================================================================

export interface ProposalValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
  completeness: number; // percentage
}

export interface SectionValidationResult {
  sectionType: ProposalSectionType;
  isComplete: boolean;
  wordCount: number;
  hasRequiredElements: boolean;
  missingElements: string[];
  qualityScore: number; // 0-100
}

// ============================================================================
// EXPORT TYPES
// ============================================================================

// ============================================================================
// CONTENT SECTION TYPES
// ============================================================================

export interface ProposalContentSection {
  id: string;
  tenantId: string;
  sectionType: string;
  title: string;
  description?: string;
  promptEn: string;
  promptAr?: string;
  systemPromptEn: string;
  systemPromptAr?: string;
  orderIndex: number;
  isActive: boolean;
  isDefault: boolean;
  createdById?: string;
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// CONTENT SECTION TYPES
// ============================================================================

export interface ProposalContentSection {
  id: string;
  tenantId: string;
  sectionType: string;
  title: string;
  description?: string;
  promptEn: string;
  promptAr?: string;
  systemPromptEn: string;
  systemPromptAr?: string;
  orderIndex: number;
  isActive: boolean;
  isDefault: boolean;
  createdById?: string;
  createdAt: Date;
  updatedAt: Date;
}

export type {
  Proposal,
  ProposalSection,
  ProposalChart,
  Document
} from '@prisma/client';
