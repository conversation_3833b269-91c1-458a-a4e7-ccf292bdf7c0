import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { aiProposalService } from '@/services/ai-proposal-generation';
import { z } from 'zod';

// Request validation schema
const generateProposalSchema = z.object({
  opportunityId: z.string().min(1, 'Opportunity ID is required'),
  title: z.string().min(1, 'Title is required').max(255),
  selectedFileIds: z.array(z.string()).min(1, 'At least one file must be selected'),
  sections: z.array(z.enum([
    'executive_summary',
    'scope_of_work',
    'architecture_high_level',
    'architecture_low_level',
    'out_of_scope',
    'assumptions',
    'project_plan',
    'resource_estimation'
  ])).min(1, 'At least one section must be selected'),
  includeCharts: z.boolean().default(false),
  chartTypes: z.array(z.enum([
    'architecture_high_level',
    'architecture_low_level',
    'project_timeline',
    'system_flow'
  ])).optional(),
  customPrompt: z.string().optional(),
  locale: z.enum(['en', 'ar']).default('en')
});

/**
 * POST /api/proposals/generate
 * Generate AI-powered proposal from opportunity and selected RFP files
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal creation
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.CREATE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    
    // Validate request data
    const validatedData = generateProposalSchema.parse(body);

    // Verify opportunity exists and belongs to tenant
    const { prisma } = await import('@/lib/prisma');
    const opportunity = await prisma.opportunity.findFirst({
      where: {
        id: validatedData.opportunityId,
        tenantId: currentTenant.id
      }
    });

    if (!opportunity) {
      return NextResponse.json(
        { error: 'Opportunity not found or access denied' },
        { status: 404 }
      );
    }

    // Verify selected files exist and belong to the opportunity
    const documents = await prisma.document.findMany({
      where: {
        id: { in: validatedData.selectedFileIds },
        tenantId: currentTenant.id,
        relatedToType: 'opportunity',
        relatedToId: validatedData.opportunityId
      }
    });

    if (documents.length !== validatedData.selectedFileIds.length) {
      return NextResponse.json(
        { error: 'Some selected files are invalid or not associated with this opportunity' },
        { status: 400 }
      );
    }

    // Validate chart types if charts are requested
    if (validatedData.includeCharts && (!validatedData.chartTypes || validatedData.chartTypes.length === 0)) {
      return NextResponse.json(
        { error: 'Chart types must be specified when includeCharts is true' },
        { status: 400 }
      );
    }

    // Start proposal generation
    const result = await aiProposalService.generateProposal(
      validatedData,
      currentTenant.id,
      session.user.id
    );

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Proposal generation started successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error generating proposal:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid request data', 
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * @swagger
 * /api/proposals/generate:
 *   post:
 *     summary: Generate AI-powered proposal
 *     description: Generate a comprehensive proposal using AI based on opportunity data and selected RFP files
 *     tags: [Proposals]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - opportunityId
 *               - title
 *               - selectedFileIds
 *               - sections
 *             properties:
 *               opportunityId:
 *                 type: string
 *                 description: ID of the opportunity to generate proposal for
 *               title:
 *                 type: string
 *                 description: Title of the proposal
 *                 maxLength: 255
 *               selectedFileIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of document IDs to use as RFP context
 *                 minItems: 1
 *               sections:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: [executive_summary, scope_of_work, architecture_high_level, architecture_low_level, out_of_scope, assumptions, project_plan, resource_estimation]
 *                 description: Sections to generate
 *                 minItems: 1
 *               includeCharts:
 *                 type: boolean
 *                 description: Whether to generate Mermaid charts
 *                 default: false
 *               chartTypes:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: [architecture_high_level, architecture_low_level, project_timeline, system_flow]
 *                 description: Types of charts to generate (required if includeCharts is true)
 *               customPrompt:
 *                 type: string
 *                 description: Optional custom prompt to guide AI generation
 *     responses:
 *       201:
 *         description: Proposal generation started successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     proposalId:
 *                       type: string
 *                     status:
 *                       type: string
 *                       enum: [pending, generating, completed, failed]
 *                     message:
 *                       type: string
 *                     estimatedCompletionTime:
 *                       type: number
 *                       description: Estimated completion time in minutes
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - insufficient permissions
 *       404:
 *         description: Opportunity not found
 *       500:
 *         description: Internal server error
 */
