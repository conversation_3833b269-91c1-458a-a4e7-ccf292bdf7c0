'use client';

import React, { useState, useEffect } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Edit3,
  Save,
  X,
  RefreshCw,
  FileText,
  BarChart3,
  CheckCircle,
  Clock,
  AlertCircle,
  Download,
  Eye,
  ChevronDown,
  ChevronRight,
  Trash2,
  Wand2,
  Loader2,
  Undo2,
  MoreVertical,
  GripVertical,
  Settings,
  Upload
} from 'lucide-react';
import { ProposalSectionWithRelations, ProposalChartWithRelations } from '@/types/proposal';
import { MermaidDiagram } from '@/components/ui/mermaid-diagram';
import { FileSelectionDialog } from '@/components/proposals/file-selection-dialog';
import { toast } from 'sonner';

interface ProposalSectionsViewProps {
  proposalId: string;
  onDocumentGenerate: () => void;
}

interface ProposalData {
  proposalId: string;
  proposalTitle: string;
  generationStatus: string;
  sections: ProposalSectionWithRelations[];
  charts: ProposalChartWithRelations[];
  statistics: {
    totalSections: number;
    completedSections: number;
    totalCharts: number;
    renderedCharts: number;
    totalWordCount: number;
    totalTokensUsed: number;
    completionPercentage: number;
  };
}

type EnhancementType = 'make_shorter' | 'make_longer' | 'change_tone' | 'custom_enhance';
type ToneType = 'professional' | 'casual' | 'technical';
type SupportedLanguage = 'en' | 'ar' | 'mixed';

interface EnhancementRequest {
  enhancementType: EnhancementType;
  toneType?: ToneType;
  customPrompt?: string;
  selectedFileIds?: string[];
}

interface EnhancementResponse {
  originalContent: string;
  enhancedContent: string;
  enhancementType: EnhancementType;
  toneType?: ToneType;
  customPrompt?: string;
  explanation: string;
  wordCountChange: number;
  tokensUsed: number;
  detectedLanguage: SupportedLanguage;
  languageConsistency: {
    isConsistent: boolean;
    originalLang: SupportedLanguage;
    enhancedLang: SupportedLanguage;
    warning?: string;
  };
}

interface SectionState {
  isCollapsed: boolean;
  isEditing: boolean;
  isEnhancing: boolean;
  pendingEnhancement?: EnhancementResponse;
}

// Helper function to get language display name
function getLanguageDisplayName(language: SupportedLanguage): string {
  switch (language) {
    case 'ar':
      return 'Arabic (العربية)';
    case 'en':
      return 'English';
    case 'mixed':
      return 'Mixed (Arabic & English)';
    default:
      return 'Unknown';
  }
}

export function ProposalSectionsView({ proposalId, onDocumentGenerate }: ProposalSectionsViewProps) {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<ProposalData | null>(null);
  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [editContent, setEditContent] = useState('');
  const [editTitle, setEditTitle] = useState('');
  const [saving, setSaving] = useState(false);
  const [regenerating, setRegenerating] = useState<string | null>(null);

  // Enhanced state for collapsible sections and AI features
  const [sectionStates, setSectionStates] = useState<Record<string, SectionState>>({});
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [sectionToDelete, setSectionToDelete] = useState<string | null>(null);
  const [chartDeleteDialogOpen, setChartDeleteDialogOpen] = useState(false);
  const [chartToDelete, setChartToDelete] = useState<{ id: string; title: string } | null>(null);
  const [enhancementPreview, setEnhancementPreview] = useState<EnhancementResponse | null>(null);
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
  const [enhancementTimeout, setEnhancementTimeout] = useState<string | null>(null);
  const [reordering, setReordering] = useState(false);

  // Custom Enhancement Dialog State
  const [customEnhanceDialogOpen, setCustomEnhanceDialogOpen] = useState(false);
  const [customEnhanceSectionId, setCustomEnhanceSectionId] = useState<string | null>(null);
  const [customPrompt, setCustomPrompt] = useState('');
  const [selectedFileIds, setSelectedFileIds] = useState<string[]>([]);
  const [fileSelectionDialogOpen, setFileSelectionDialogOpen] = useState(false);
  const [customEnhancing, setCustomEnhancing] = useState(false);

  useEffect(() => {
    loadProposalData();
  }, [proposalId]);

  // Initialize section states when data loads
  useEffect(() => {
    if (data?.sections) {
      const initialStates: Record<string, SectionState> = {};
      data.sections.forEach(section => {
        initialStates[section.id] = {
          isCollapsed: true, // Default collapsed state
          isEditing: false,
          isEnhancing: false,
          pendingEnhancement: undefined
        };
      });
      setSectionStates(initialStates);
    }
  }, [data?.sections]);

  // Helper function to get section state
  const getSectionState = (sectionId: string): SectionState => {
    return sectionStates[sectionId] || {
      isCollapsed: true,
      isEditing: false,
      isEnhancing: false,
      pendingEnhancement: undefined
    };
  };

  // Helper function to update section state
  const updateSectionState = (sectionId: string, updates: Partial<SectionState>) => {
    setSectionStates(prev => ({
      ...prev,
      [sectionId]: {
        ...getSectionState(sectionId),
        ...updates
      }
    }));
  };

  const loadProposalData = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/proposals/${proposalId}/sections`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to load proposal data');
      }

      setData(result.data);
    } catch (error) {
      console.error('Error loading proposal data:', error);
      toast.error('Failed to load proposal data');
    } finally {
      setLoading(false);
    }
  };

  const handleEditSection = (section: ProposalSectionWithRelations) => {
    setEditingSection(section.id);
    setEditContent(section.content || '');
    setEditTitle(section.title);
    updateSectionState(section.id, { isEditing: true, isCollapsed: false });
  };

  const handleCancelEdit = () => {
    if (editingSection) {
      updateSectionState(editingSection, { isEditing: false });
    }
    setEditingSection(null);
    setEditContent('');
    setEditTitle('');
  };

  const handleSaveSection = async () => {
    if (!editingSection) return;

    setSaving(true);
    try {
      const response = await fetch(`/api/proposals/${proposalId}/sections/${editingSection}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: editTitle,
          content: editContent,
          status: 'reviewed'
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to save section');
      }

      toast.success('Section saved successfully');
      setEditingSection(null);
      loadProposalData(); // Reload to get updated data

    } catch (error) {
      console.error('Error saving section:', error);
      toast.error('Failed to save section');
    } finally {
      setSaving(false);
    }
  };

  const handleRegenerateSection = async (sectionId: string) => {
    setRegenerating(sectionId);
    try {
      const response = await fetch(`/api/proposals/${proposalId}/regenerate-section`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sectionId,
          preserveManualEdits: false,
          locale: data?.proposal?.locale || 'en'
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to regenerate section');
      }

      toast.success('Section regenerated successfully');
      loadProposalData(); // Reload to get updated data

    } catch (error) {
      console.error('Error regenerating section:', error);
      toast.error('Failed to regenerate section');
    } finally {
      setRegenerating(null);
    }
  };

  // AI Enhancement Functions
  const handleEnhanceSection = async (
    sectionId: string,
    enhancementType: EnhancementType,
    toneType?: ToneType,
    customPrompt?: string,
    selectedFileIds?: string[]
  ) => {
    updateSectionState(sectionId, { isEnhancing: true });

    // Show timeout warning after 10 seconds
    const timeoutWarning = setTimeout(() => {
      setEnhancementTimeout(sectionId);
      toast.info('Enhancement is taking longer than expected. Please wait...');
    }, 10000);

    try {
      const response = await fetch(`/api/proposals/${proposalId}/sections/${sectionId}/enhance`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          enhancementType,
          toneType,
          locale: data?.proposal?.locale || 'en'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));

        if (response.status === 408) {
          throw new Error('Enhancement request timed out. Please try again with shorter content.');
        }

        throw new Error(errorData.error || 'Failed to enhance section');
      }

      const enhancementResult: EnhancementResponse = await response.json();

      // Show preview dialog
      setEnhancementPreview(enhancementResult);
      setPreviewDialogOpen(true);

      toast.success('Content enhanced successfully');
    } catch (error) {
      console.error('Enhancement error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to enhance section content';
      toast.error(errorMessage);
    } finally {
      clearTimeout(timeoutWarning);
      setEnhancementTimeout(null);
      updateSectionState(sectionId, { isEnhancing: false });
    }
  };

  const handleApplyEnhancement = async () => {
    if (!enhancementPreview) return;

    const sectionId = data?.sections.find(s => s.content === enhancementPreview.originalContent)?.id;
    if (!sectionId) return;

    setSaving(true);
    try {
      const response = await fetch(`/api/proposals/${proposalId}/sections/${sectionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: enhancementPreview.enhancedContent,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to apply enhancement');
      }

      toast.success('Enhancement applied successfully');
      setPreviewDialogOpen(false);
      setEnhancementPreview(null);
      loadProposalData();
    } catch (error) {
      console.error('Apply enhancement error:', error);
      toast.error('Failed to apply enhancement');
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteSection = async () => {
    if (!sectionToDelete) return;

    try {
      const response = await fetch(`/api/proposals/${proposalId}/sections/${sectionToDelete}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete section');
      }

      toast.success('Section deleted successfully');
      setDeleteDialogOpen(false);
      setSectionToDelete(null);
      loadProposalData();
    } catch (error) {
      console.error('Delete section error:', error);
      toast.error('Failed to delete section');
    }
  };

  const handleDeleteChart = async () => {
    if (!chartToDelete) return;

    try {
      const response = await fetch(`/api/charts/${chartToDelete.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete chart');
      }

      toast.success('Chart deleted successfully');
      setChartDeleteDialogOpen(false);
      setChartToDelete(null);

      // Update local state to remove the deleted chart
      setData(prevData => {
        if (!prevData) return prevData;

        return {
          ...prevData,
          charts: prevData.charts.filter(c => c.id !== chartToDelete.id)
        };
      });
    } catch (error) {
      console.error('Delete chart error:', error);
      toast.error('Failed to delete chart');
    }
  };

  const handleRenderChart = async (chartId: string) => {
    try {
      const response = await fetch(`/api/charts/${chartId}/render`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          format: 'png',
          theme: 'default',
          width: 800,
          height: 600
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to render chart');
      }

      toast.success('Chart rendered successfully');
      loadProposalData(); // Reload to get updated data

    } catch (error) {
      console.error('Error rendering chart:', error);
      toast.error('Failed to render chart');
    }
  };

  const handleDragEnd = async (result: DropResult) => {
    if (!result.destination || !data) return;

    const { source, destination } = result;

    // If dropped in the same position, do nothing
    if (source.index === destination.index) {
      return;
    }

    setReordering(true);

    try {
      // Create a new array with reordered sections
      const reorderedSections = Array.from(data.sections);
      const [movedSection] = reorderedSections.splice(source.index, 1);
      reorderedSections.splice(destination.index, 0, movedSection);

      // Update local state optimistically
      setData(prevData => {
        if (!prevData) return prevData;
        return {
          ...prevData,
          sections: reorderedSections
        };
      });

      // Prepare the reorder request
      const sectionOrders = reorderedSections.map((section, index) => ({
        id: section.id,
        orderIndex: index
      }));

      // Send reorder request to API
      const response = await fetch(`/api/proposals/${proposalId}/sections/reorder`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sectionOrders }),
      });

      if (!response.ok) {
        throw new Error('Failed to reorder sections');
      }

      toast.success('Sections reordered successfully');
    } catch (error) {
      console.error('Error reordering sections:', error);
      toast.error('Failed to reorder sections');
      // Reload data to revert optimistic update
      loadProposalData();
    } finally {
      setReordering(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'generated':
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'draft':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'reviewed':
        return <Eye className="h-4 w-4 text-blue-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'generated':
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      case 'reviewed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!data) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load proposal data. Please try refreshing the page.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Statistics */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{data.proposalTitle}</CardTitle>
              <CardDescription>
                AI-generated proposal with {data.statistics.totalSections} sections
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline">
                {data.statistics.completionPercentage}% Complete
              </Badge>
              <Button onClick={onDocumentGenerate} size="sm">
                <Download className="h-4 w-4 mr-2" />
                Generate Document
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="font-medium">Sections</div>
              <div className="text-gray-500">
                {data.statistics.completedSections} / {data.statistics.totalSections}
              </div>
            </div>
            <div>
              <div className="font-medium">Charts</div>
              <div className="text-gray-500">
                {data.statistics.renderedCharts} / {data.statistics.totalCharts}
              </div>
            </div>
            <div>
              <div className="font-medium">Word Count</div>
              <div className="text-gray-500">{data.statistics.totalWordCount.toLocaleString()}</div>
            </div>
            <div>
              <div className="font-medium">AI Tokens</div>
              <div className="text-gray-500">{data.statistics.totalTokensUsed.toLocaleString()}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sections */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium flex items-center">
          <FileText className="h-5 w-5 mr-2" />
          Proposal Sections
          {reordering && (
            <Badge variant="outline" className="ml-2">
              <Loader2 className="h-3 w-3 mr-1 animate-spin" />
              Reordering...
            </Badge>
          )}
        </h3>

        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="sections">
            {(provided) => (
              <div
                {...provided.droppableProps}
                ref={provided.innerRef}
                className="space-y-4"
              >
                {data.sections.map((section, index) => {
                  const sectionState = getSectionState(section.id);

                  return (
                    <Draggable
                      key={section.id}
                      draggableId={section.id}
                      index={index}
                      isDragDisabled={reordering || editingSection === section.id}
                    >
                      {(provided, snapshot) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          className={`proposal-section-transition ${
                            snapshot.isDragging ? 'proposal-section-dragging' : ''
                          }`}
                        >
                          <Collapsible
                            open={!sectionState.isCollapsed}
                            onOpenChange={(open) => updateSectionState(section.id, { isCollapsed: !open })}
                          >
                            <Card>
                <CollapsibleTrigger asChild>
                  <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div
                          {...provided.dragHandleProps}
                          className="proposal-section-drag-handle cursor-grab active:cursor-grabbing p-1 rounded"
                          title="Drag to reorder"
                        >
                          <GripVertical className="h-4 w-4 text-muted-foreground" />
                        </div>
                        {sectionState.isCollapsed ? (
                          <ChevronRight className="h-4 w-4 text-muted-foreground" />
                        ) : (
                          <ChevronDown className="h-4 w-4 text-muted-foreground" />
                        )}
                        {getStatusIcon(section.status)}
                        <div>
                          <CardTitle className="text-base">
                            {editingSection === section.id ? (
                              <Input
                                value={editTitle}
                                onChange={(e) => setEditTitle(e.target.value)}
                                className="font-medium"
                                onClick={(e) => e.stopPropagation()}
                              />
                            ) : (
                              section.title
                            )}
                          </CardTitle>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge className={getStatusColor(section.status)} variant="secondary">
                              {section.status}
                            </Badge>
                            {section.wordCount && (
                              <span className="text-sm text-muted-foreground">{section.wordCount} words</span>
                            )}
                            {section.isAiGenerated && (
                              <span className="text-sm text-blue-600">AI Generated</span>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Section Toolbar */}
                      <div className="flex items-center space-x-2" onClick={(e) => e.stopPropagation()}>
                        {editingSection === section.id ? (
                          <>
                            <Button
                              size="sm"
                              onClick={handleSaveSection}
                              disabled={saving}
                            >
                              <Save className="h-4 w-4 mr-1" />
                              {saving ? 'Saving...' : 'Save'}
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={handleCancelEdit}
                              disabled={saving}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </>
                        ) : (
                          <>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEditSection(section)}
                            >
                              <Edit3 className="h-4 w-4" />
                            </Button>

                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setSectionToDelete(section.id);
                                setDeleteDialogOpen(true);
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>

                            {/* AI Enhancements Dropdown */}
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  disabled={sectionState.isEnhancing}
                                  title={sectionState.isEnhancing ? 'Enhancing content...' : 'AI Enhancement Options'}
                                >
                                  {sectionState.isEnhancing ? (
                                    <>
                                      <Loader2 className="h-4 w-4 animate-spin mr-1" />
                                      {enhancementTimeout === section.id && (
                                        <span className="text-xs">Taking longer...</span>
                                      )}
                                    </>
                                  ) : (
                                    <Wand2 className="h-4 w-4" />
                                  )}
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end" className="w-48">
                                <DropdownMenuItem
                                  onClick={() => handleEnhanceSection(section.id, 'make_shorter')}
                                  disabled={sectionState.isEnhancing}
                                >
                                  <Wand2 className="h-4 w-4 mr-2" />
                                  Make Shorter
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleEnhanceSection(section.id, 'make_longer')}
                                  disabled={sectionState.isEnhancing}
                                >
                                  <Wand2 className="h-4 w-4 mr-2" />
                                  Make Longer
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuSub>
                                  <DropdownMenuSubTrigger disabled={sectionState.isEnhancing}>
                                    <Wand2 className="h-4 w-4 mr-2" />
                                    Change Tone
                                  </DropdownMenuSubTrigger>
                                  <DropdownMenuSubContent>
                                    <DropdownMenuItem
                                      onClick={() => handleEnhanceSection(section.id, 'change_tone', 'professional')}
                                    >
                                      Professional
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                      onClick={() => handleEnhanceSection(section.id, 'change_tone', 'casual')}
                                    >
                                      Casual
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                      onClick={() => handleEnhanceSection(section.id, 'change_tone', 'technical')}
                                    >
                                      Technical
                                    </DropdownMenuItem>
                                  </DropdownMenuSubContent>
                                </DropdownMenuSub>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => handleRegenerateSection(section.id)}
                                  disabled={regenerating === section.id}
                                >
                                  <RefreshCw className={`h-4 w-4 mr-2 ${regenerating === section.id ? 'animate-spin' : ''}`} />
                                  Regenerate
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                </CollapsibleTrigger>

                <CollapsibleContent>
                  <CardContent className="pt-0">
                    {editingSection === section.id ? (
                      <Textarea
                        value={editContent}
                        onChange={(e) => setEditContent(e.target.value)}
                        rows={10}
                        className="font-mono text-sm"
                      />
                    ) : (
                      <div className="prose max-w-none">
                        <div className="whitespace-pre-wrap text-sm">
                          {section.content || 'No content available'}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </CollapsibleContent>
                            </Card>
                          </Collapsible>
                        </div>
                      )}
                    </Draggable>
                  );
                })}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      </div>

      {/* Charts */}
      {data.charts.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Architecture Diagrams
          </h3>
          
          {data.charts.map((chart) => (
            <Card key={chart.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(chart.status)}
                    <div>
                      <CardTitle className="text-base">{chart.title}</CardTitle>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge className={getStatusColor(chart.status)} variant="secondary">
                          {chart.status}
                        </Badge>
                        {chart.isAiGenerated && (
                          <span className="text-sm text-blue-600">AI Generated</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {chart.renderedImagePath ? (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => window.open(`/api/charts/${chart.id}/image`, '_blank')}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View Image
                      </Button>
                    ) : (
                      <Button
                        size="sm"
                        onClick={() => handleRenderChart(chart.id)}
                      >
                        <BarChart3 className="h-4 w-4 mr-1" />
                        Render Chart
                      </Button>
                    )}

                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        setChartToDelete({ id: chart.id, title: chart.title });
                        setChartDeleteDialogOpen(true);
                      }}
                      className="text-red-600 border-red-300 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {chart.description && (
                  <p className="text-sm text-gray-600 mb-3">{chart.description}</p>
                )}

                {/* Enhanced Interactive Mermaid Diagram */}
                <MermaidDiagram
                  code={chart.mermaidCode}
                  title={chart.title}
                  theme="default"
                  showControls={true}
                  className="mt-4"
                  chartId={chart.id}
                  proposalId={proposalId}
                  showFixButton={true}
                  showRegenerateButton={true}
                  autoRender={true}
                  enablePanZoom={true}
                  showZoomControls={true}
                  showFullscreenButton={true}
                  maxZoom={3}
                  minZoom={0.1}
                  onError={(error) => {
                    console.error('Mermaid rendering error:', error);
                    toast.error('Failed to render diagram');
                  }}
                  onSuccess={() => {
                    console.log('Mermaid diagram rendered successfully');
                  }}
                  onChartRegenerated={(newCode) => {
                    console.log('Chart regenerated with new code:', newCode);
                    // Update the specific chart in local state instead of reloading all data
                    setData(prevData => {
                      if (!prevData) return prevData;

                      return {
                        ...prevData,
                        charts: prevData.charts.map(c =>
                          c.id === chart.id
                            ? { ...c, mermaidCode: newCode }
                            : c
                        )
                      };
                    });
                  }}
                />
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Delete Section Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Section</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this section? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setDeleteDialogOpen(false);
                setSectionToDelete(null);
              }}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteSection}
            >
              Delete Section
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Chart Confirmation Dialog */}
      <Dialog open={chartDeleteDialogOpen} onOpenChange={setChartDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Chart</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the chart "{chartToDelete?.title}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setChartDeleteDialogOpen(false);
                setChartToDelete(null);
              }}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteChart}
            >
              Delete Chart
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Enhancement Preview Dialog */}
      <Dialog open={previewDialogOpen} onOpenChange={setPreviewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>AI Enhancement Preview</DialogTitle>
            <DialogDescription>
              {enhancementPreview?.explanation}
            </DialogDescription>
          </DialogHeader>

          {enhancementPreview && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Original Content</Label>
                  <div className="mt-2 p-3 bg-muted rounded-md max-h-60 overflow-y-auto">
                    <div className="text-sm whitespace-pre-wrap">
                      {enhancementPreview.originalContent}
                    </div>
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium">Enhanced Content</Label>
                  <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-md max-h-60 overflow-y-auto">
                    <div className="text-sm whitespace-pre-wrap">
                      {enhancementPreview.enhancedContent}
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span>Enhancement Type: {enhancementPreview.enhancementType.replace('_', ' ')}</span>
                  {enhancementPreview.toneType && (
                    <span>Tone: {enhancementPreview.toneType}</span>
                  )}
                  <span>Word Count Change: {enhancementPreview.wordCountChange > 0 ? '+' : ''}{enhancementPreview.wordCountChange}</span>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">
                    Language: <span className="font-medium">{getLanguageDisplayName(enhancementPreview.detectedLanguage)}</span>
                  </span>
                  {!enhancementPreview.languageConsistency.isConsistent && (
                    <span className="text-amber-600 text-xs flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      Language Warning
                    </span>
                  )}
                </div>

                {!enhancementPreview.languageConsistency.isConsistent && (
                  <div className="p-2 bg-amber-50 border border-amber-200 rounded text-xs text-amber-800">
                    <strong>Language Consistency Warning:</strong> {enhancementPreview.languageConsistency.warning}
                  </div>
                )}
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setPreviewDialogOpen(false);
                setEnhancementPreview(null);
              }}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button
              onClick={handleApplyEnhancement}
              disabled={saving}
            >
              {saving ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Wand2 className="h-4 w-4 mr-2" />
              )}
              Apply Enhancement
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
