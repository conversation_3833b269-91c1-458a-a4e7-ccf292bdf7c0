'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Wand2,
  FileText,
  BarChart3,
  CheckCircle,
  AlertCircle,
  <PERSON>,
  ArrowR<PERSON>,
  ArrowLeft,
  Eye,
  RefreshCw,
  Settings,
  Layers,
  Upload
} from 'lucide-react';
import { FileSelectionDialog } from './file-selection-dialog';
import { ProposalSectionType, ProposalChartType } from '@/types/proposal';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface ProposalGenerationWizardProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  opportunityId: string;
  opportunityTitle: string;
  onProposalGenerated: (proposalId: string) => void;
}

interface WizardStep {
  id: string;
  title: string;
  description: string;
  completed: boolean;
}

const SECTION_OPTIONS: { value: ProposalSectionType; label: string; description: string; icon: React.ComponentType<{ className?: string }> }[] = [
  { value: 'executive_summary', label: 'Executive Summary', description: 'High-level overview and business value', icon: FileText },
  { value: 'scope_of_work', label: 'Scope of Work', description: 'Detailed project requirements', icon: Layers },
  { value: 'architecture_high_level', label: 'High-Level Architecture', description: 'System overview and components', icon: BarChart3 },
  { value: 'architecture_low_level', label: 'Technical Architecture', description: 'Detailed technical implementation', icon: Settings },
  { value: 'out_of_scope', label: 'Out of Scope', description: 'What is not included', icon: AlertCircle },
  { value: 'assumptions', label: 'Assumptions', description: 'Project assumptions and constraints', icon: CheckCircle },
  { value: 'project_plan', label: 'Project Plan', description: 'Timeline and milestones', icon: Clock },
  { value: 'resource_estimation', label: 'Resource Estimation', description: 'Team size and effort estimation', icon: Upload }
];

const CHART_OPTIONS: { value: ProposalChartType; label: string; description: string; icon: React.ComponentType<{ className?: string }> }[] = [
  { value: 'architecture_high_level', label: 'High-Level Architecture', description: 'System overview diagram', icon: BarChart3 },
  { value: 'architecture_low_level', label: 'Technical Architecture', description: 'Detailed technical diagram', icon: Settings },
  { value: 'project_timeline', label: 'Project Timeline', description: 'Project phases and milestones', icon: Clock },
  { value: 'system_flow', label: 'System Flow', description: 'Data and process flow', icon: Layers }
];

export function ProposalGenerationWizard({
  open,
  onOpenChange,
  opportunityId,
  opportunityTitle,
  onProposalGenerated
}: ProposalGenerationWizardProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [fileSelectionOpen, setFileSelectionOpen] = useState(false);
  
  // Form data
  const [title, setTitle] = useState(`Proposal for ${opportunityTitle}`);
  const [selectedSections, setSelectedSections] = useState<ProposalSectionType[]>([
    'executive_summary',
    'scope_of_work',
    'architecture_high_level'
  ]);
  const [includeCharts, setIncludeCharts] = useState(true);
  const [selectedCharts, setSelectedCharts] = useState<ProposalChartType[]>([
    'architecture_high_level'
  ]);
  const [customPrompt, setCustomPrompt] = useState('');
  const [selectedFileIds, setSelectedFileIds] = useState<string[]>([]);
  const [locale, setLocale] = useState<'en' | 'ar'>('en');
  const [fileValidationStatus, setFileValidationStatus] = useState<'idle' | 'validating' | 'completed' | 'failed'>('idle');
  const [validationResults, setValidationResults] = useState<any[]>([]);

  // Generation state
  const [generationStatus, setGenerationStatus] = useState<'idle' | 'generating' | 'completed' | 'failed'>('idle');
  const [progress, setProgress] = useState(0);

  const steps: WizardStep[] = [
    {
      id: 'basic',
      title: 'Basic Information',
      description: 'Set proposal title and basic configuration',
      completed: currentStep > 0
    },
    {
      id: 'sections',
      title: 'Content Sections',
      description: 'Choose which sections to generate',
      completed: currentStep > 1
    },
    {
      id: 'files',
      title: 'RFP Files',
      description: 'Select files for AI context',
      completed: currentStep > 2
    },
    {
      id: 'generate',
      title: 'Generate',
      description: 'AI proposal generation',
      completed: generationStatus === 'completed'
    }
  ];

  const canProceed = () => {
    switch (currentStep) {
      case 0:
        return title.trim().length > 0;
      case 1:
        return selectedSections.length > 0;
      case 2:
        return selectedFileIds.length > 0;
      default:
        return true;
    }
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSectionToggle = (section: ProposalSectionType, checked: boolean) => {
    setSelectedSections(prev => {
      if (checked) {
        return [...prev, section];
      } else {
        return prev.filter(s => s !== section);
      }
    });
  };

  const handleChartToggle = (chart: ProposalChartType, checked: boolean) => {
    setSelectedCharts(prev => {
      if (checked) {
        return [...prev, chart];
      } else {
        return prev.filter(c => c !== chart);
      }
    });
  };

  const handleFilesSelected = async (fileIds: string[]) => {
    setSelectedFileIds(fileIds);

    // Validate files for Gemini compatibility
    if (fileIds.length > 0) {
      setFileValidationStatus('validating');
      try {
        const response = await fetch(`/api/proposals/temp/validate-files`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            documentIds: fileIds
          }),
        });

        const result = await response.json();

        if (response.ok) {
          setValidationResults(result.data.validationResults || []);
          setFileValidationStatus('completed');
        } else {
          setFileValidationStatus('failed');
          toast.error('Failed to validate files for Gemini compatibility');
        }
      } catch (error) {
        console.error('File validation error:', error);
        setFileValidationStatus('failed');
        toast.error('Error validating files');
      }
    } else {
      setFileValidationStatus('idle');
      setValidationResults([]);
    }
  };

  const handleGenerate = async () => {
    setLoading(true);
    setGenerationStatus('generating');
    setProgress(0);

    try {
      const response = await fetch('/api/proposals/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          opportunityId,
          title,
          selectedFileIds,
          sections: selectedSections,
          includeCharts,
          chartTypes: includeCharts ? selectedCharts : [],
          customPrompt: customPrompt.trim() || undefined,
          locale
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to generate proposal');
      }

      setGeneratedProposalId(result.data.proposalId);
      setGenerationStatus('completed');
      setProgress(100);
      
      toast.success('Proposal generated successfully!');
      onProposalGenerated(result.data.proposalId);

    } catch (error) {
      console.error('Error generating proposal:', error);
      setGenerationStatus('failed');
      toast.error('Failed to generate proposal');
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-8">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                <Wand2 className="h-8 w-8 text-primary" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-foreground">Configure Your Proposal</h3>
                <p className="text-sm text-muted-foreground">Set up the basic information for AI generation</p>
              </div>
            </div>

            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="title" className="text-sm font-medium">
                  Proposal Title *
                </Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Enter a descriptive title for your proposal"
                  className="h-11"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="locale" className="text-sm font-medium">
                  Content Language
                </Label>
                <Select value={locale} onValueChange={(value: 'en' | 'ar') => setLocale(value)}>
                  <SelectTrigger className="h-11">
                    <SelectValue placeholder="Select content language" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="ar">العربية (Arabic)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="customPrompt" className="text-sm font-medium">
                  Custom Instructions <span className="text-muted-foreground">(Optional)</span>
                </Label>
                <Textarea
                  id="customPrompt"
                  value={customPrompt}
                  onChange={(e) => setCustomPrompt(e.target.value)}
                  placeholder={locale === 'ar'
                    ? "قدم تعليمات أو متطلبات محددة للذكاء الاصطناعي..."
                    : "Provide specific instructions or requirements for the AI..."
                  }
                  className="min-h-[120px] resize-none"
                  rows={5}
                  dir={locale === 'ar' ? 'rtl' : 'ltr'}
                />
              </div>
            </div>
          </div>
        );

      case 1:
        return (
          <div className="space-y-8">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                <Layers className="h-8 w-8 text-primary" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-foreground">Choose Content Sections</h3>
                <p className="text-sm text-muted-foreground">Select which sections to include in your proposal</p>
              </div>
            </div>

            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h4 className="text-base font-semibold">Content Sections</h4>
                <Badge variant="secondary" className="text-xs">
                  {selectedSections.length} selected
                </Badge>
              </div>

              <div className="grid gap-3">
                {SECTION_OPTIONS.map((section) => {
                  const IconComponent = section.icon;
                  return (
                    <Card
                      key={section.value}
                      className={cn(
                        "p-4 transition-all duration-200 hover:shadow-sm cursor-pointer border",
                        selectedSections.includes(section.value)
                          ? 'ring-2 ring-primary bg-primary/5 border-primary/20'
                          : 'hover:bg-muted/50'
                      )}
                      onClick={() => handleSectionToggle(section.value, !selectedSections.includes(section.value))}
                    >
                      <div className="flex items-center space-x-3">
                        <Checkbox
                          checked={selectedSections.includes(section.value)}
                          onCheckedChange={(checked) =>
                            handleSectionToggle(section.value, checked as boolean)
                          }
                        />
                        <IconComponent className="h-5 w-5 text-muted-foreground" />
                        <div className="flex-1">
                          <div className="font-medium">{section.label}</div>
                          <div className="text-sm text-muted-foreground">{section.description}</div>
                        </div>
                      </div>
                    </Card>
                  );
                })}
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      checked={includeCharts}
                      onCheckedChange={(checked) => setIncludeCharts(checked as boolean)}
                    />
                    <BarChart3 className="h-5 w-5 text-muted-foreground" />
                    <Label className="text-base font-semibold">Include Diagrams</Label>
                  </div>
                  {includeCharts && (
                    <Badge variant="secondary" className="text-xs">
                      {selectedCharts.length} selected
                    </Badge>
                  )}
                </div>

                {includeCharts && (
                  <div className="grid gap-3 pl-8">
                    {CHART_OPTIONS.map((chart) => {
                      const IconComponent = chart.icon;
                      return (
                        <Card
                          key={chart.value}
                          className={cn(
                            "p-3 transition-all duration-200 hover:shadow-sm cursor-pointer border",
                            selectedCharts.includes(chart.value)
                              ? 'ring-2 ring-primary bg-primary/5 border-primary/20'
                              : 'hover:bg-muted/50'
                          )}
                          onClick={() => handleChartToggle(chart.value, !selectedCharts.includes(chart.value))}
                        >
                          <div className="flex items-center space-x-3">
                            <Checkbox
                              checked={selectedCharts.includes(chart.value)}
                              onCheckedChange={(checked) =>
                                handleChartToggle(chart.value, checked as boolean)
                              }
                            />
                            <IconComponent className="h-4 w-4 text-muted-foreground" />
                            <div className="flex-1">
                              <div className="font-medium text-sm">{chart.label}</div>
                              <div className="text-xs text-muted-foreground">{chart.description}</div>
                            </div>
                          </div>
                        </Card>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-8">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                <Upload className="h-8 w-8 text-primary" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-foreground">Select RFP Files</h3>
                <p className="text-sm text-muted-foreground">Choose documents for AI context analysis</p>
              </div>
            </div>

            <div className="space-y-6">
              <div className="text-center">
                <Button
                  onClick={() => setFileSelectionOpen(true)}
                  size="lg"
                  className="min-w-[200px]"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Select Files ({selectedFileIds.length} selected)
                </Button>
              </div>

              {selectedFileIds.length > 0 && (
                <div className="space-y-4">
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>{selectedFileIds.length}</strong> file{selectedFileIds.length !== 1 ? 's' : ''} selected for AI analysis.
                    </AlertDescription>
                  </Alert>

                  {fileValidationStatus === 'validating' && (
                    <Alert>
                      <RefreshCw className="h-4 w-4 animate-spin" />
                      <AlertDescription>
                        Validating files for Gemini API compatibility...
                      </AlertDescription>
                    </Alert>
                  )}

                  {fileValidationStatus === 'completed' && validationResults.length > 0 && (
                    <div className="space-y-2">
                      {validationResults.some(r => !r.isValid) && (
                        <Alert variant="destructive">
                          <AlertCircle className="h-4 w-4" />
                          <AlertDescription>
                            Some files are not compatible with Gemini API and will be excluded from AI context.
                          </AlertDescription>
                        </Alert>
                      )}

                      <div className="text-sm space-y-1">
                        <div className="font-medium">File Validation Results:</div>
                        {validationResults.map((result, index) => (
                          <div key={index} className={cn(
                            "flex items-center space-x-2 text-xs",
                            result.isValid ? "text-green-600" : "text-red-600"
                          )}>
                            {result.isValid ? (
                              <CheckCircle className="h-3 w-3" />
                            ) : (
                              <AlertCircle className="h-3 w-3" />
                            )}
                            <span className="truncate">{result.name}</span>
                            {!result.isValid && result.error && (
                              <span className="text-muted-foreground">- {result.error}</span>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {selectedFileIds.length === 0 && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Please select at least one file to provide context for AI generation.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-8">
            {generationStatus === 'idle' && (
              <>
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                    <Wand2 className="h-8 w-8 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-foreground">Ready to Generate</h3>
                    <p className="text-sm text-muted-foreground">Review your configuration and start generation</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-lg mx-auto">
                  <Card className="p-4 text-center">
                    <FileText className="h-6 w-6 mx-auto mb-2 text-primary" />
                    <div className="font-semibold text-lg">{selectedSections.length}</div>
                    <div className="text-xs text-muted-foreground">Sections</div>
                  </Card>
                  <Card className="p-4 text-center">
                    <BarChart3 className="h-6 w-6 mx-auto mb-2 text-primary" />
                    <div className="font-semibold text-lg">{includeCharts ? selectedCharts.length : 0}</div>
                    <div className="text-xs text-muted-foreground">Diagrams</div>
                  </Card>
                  <Card className="p-4 text-center">
                    <Upload className="h-6 w-6 mx-auto mb-2 text-primary" />
                    <div className="font-semibold text-lg">{selectedFileIds.length}</div>
                    <div className="text-xs text-muted-foreground">Files</div>
                  </Card>
                </div>
              </>
            )}

            {generationStatus === 'generating' && (
              <div className="text-center space-y-6">
                <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-foreground mb-2">Generating Proposal</h3>
                  <p className="text-sm text-muted-foreground">
                    AI is analyzing your files and creating content...
                  </p>
                </div>
                <div className="max-w-sm mx-auto space-y-2">
                  <Progress value={progress} className="w-full h-2" />
                  <p className="text-xs text-muted-foreground">
                    This may take a few minutes
                  </p>
                </div>
              </div>
            )}

            {generationStatus === 'completed' && (
              <div className="text-center space-y-6">
                <div className="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-green-900 mb-2">Proposal Generated!</h3>
                  <p className="text-sm text-muted-foreground">
                    Your proposal is ready for review
                  </p>
                </div>
                <Button onClick={() => onOpenChange(false)} size="lg" className="min-w-[160px]">
                  <Eye className="h-4 w-4 mr-2" />
                  View Proposal
                </Button>
              </div>
            )}

            {generationStatus === 'failed' && (
              <div className="text-center space-y-6">
                <div className="w-16 h-16 mx-auto bg-destructive/10 rounded-full flex items-center justify-center">
                  <AlertCircle className="h-8 w-8 text-destructive" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-destructive mb-2">Generation Failed</h3>
                  <p className="text-sm text-muted-foreground">
                    Please check your settings and try again
                  </p>
                </div>
                <Button onClick={handleGenerate} variant="outline" size="lg" className="min-w-[140px]">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry
                </Button>
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0 pb-6">
            <DialogTitle className="text-xl font-semibold">AI Proposal Generation</DialogTitle>
            <DialogDescription className="text-sm text-muted-foreground">
              Generate a comprehensive proposal using AI
            </DialogDescription>
          </DialogHeader>

          {/* Progress Steps */}
          <div className="flex-shrink-0 px-6 py-6 border-b bg-muted/30">
            <div className="flex items-center justify-between max-w-2xl mx-auto">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center flex-1">
                  <div className="flex flex-col items-center">
                    <div className={cn(
                      "w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300",
                      index <= currentStep
                        ? 'bg-primary text-primary-foreground shadow-lg'
                        : 'bg-muted text-muted-foreground'
                    )}>
                      {step.completed ? <CheckCircle className="h-5 w-5" /> : index + 1}
                    </div>
                    <div className="mt-3 text-center">
                      <div className={cn(
                        "text-xs font-medium transition-colors",
                        index <= currentStep ? 'text-primary' : 'text-muted-foreground'
                      )}>
                        {step.title}
                      </div>
                    </div>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={cn(
                      "flex-1 h-0.5 mx-4 mt-[-16px] transition-all duration-300",
                      index < currentStep ? 'bg-primary' : 'bg-muted'
                    )} />
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Step Content */}
          <div className="flex-1 overflow-hidden flex flex-col">
            <ScrollArea className="flex-1 px-6 py-8">
              <div className="max-w-2xl mx-auto">
                {renderStepContent()}
              </div>
            </ScrollArea>
          </div>

          {/* Navigation */}
          <div className="flex-shrink-0 px-6 py-6 border-t bg-muted/30">
            <div className="flex justify-between items-center max-w-2xl mx-auto">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 0 || loading}
                className="min-w-[100px]"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>

              <div className="text-sm text-muted-foreground">
                Step {currentStep + 1} of {steps.length}
              </div>

              {currentStep < steps.length - 1 ? (
                <Button
                  onClick={handleNext}
                  disabled={!canProceed() || loading}
                  className="min-w-[100px]"
                >
                  Next
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              ) : (
                <Button
                  onClick={handleGenerate}
                  disabled={!canProceed() || loading || generationStatus === 'completed'}
                  className="min-w-[140px]"
                  loading={loading}
                >
                  {loading ? (
                    'Generating...'
                  ) : (
                    <>
                      Generate Proposal
                      <Wand2 className="h-4 w-4 ml-2" />
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <FileSelectionDialog
        open={fileSelectionOpen}
        onOpenChange={setFileSelectionOpen}
        opportunityId={opportunityId}
        onFilesSelected={handleFilesSelected}
      />
    </>
  );
}
