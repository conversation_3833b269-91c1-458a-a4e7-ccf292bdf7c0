import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { hasPermission } from '@/lib/permissions';
import { PermissionResource, PermissionAction } from '@/types';
import { prisma } from '@/lib/prisma';
import { aiService } from '@/services/ai-service';
import { geminiFileService } from '@/services/gemini-file-service';
import { z } from 'zod';
import { detectLanguage, getLanguageInstructions, validateLanguageConsistency, getLanguageName, type SupportedLanguage } from '@/utils/language-detection';
import { GeminiFileContext } from '@/types/proposal';

// Request validation schema
const enhanceSectionSchema = z.object({
  enhancementType: z.enum(['make_shorter', 'make_longer', 'change_tone', 'custom_enhance']),
  toneType: z.enum(['professional', 'casual', 'technical']).optional(),
  customPrompt: z.string().optional(),
  selectedFileIds: z.array(z.string()).optional(),
  locale: z.enum(['en', 'ar']).optional()
});

export type EnhancementType = 'make_shorter' | 'make_longer' | 'change_tone' | 'custom_enhance';
export type ToneType = 'professional' | 'casual' | 'technical';

interface EnhancementResponse {
  originalContent: string;
  enhancedContent: string;
  enhancementType: EnhancementType;
  toneType?: ToneType;
  customPrompt?: string;
  explanation: string;
  wordCountChange: number;
  tokensUsed: number;
  detectedLanguage: SupportedLanguage;
  languageConsistency: {
    isConsistent: boolean;
    originalLang: SupportedLanguage;
    enhancedLang: SupportedLanguage;
    warning?: string;
  };
}

/**
 * POST /api/proposals/[id]/sections/[sectionId]/enhance
 * Enhance section content using AI
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string; sectionId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current tenant
    const currentTenant = session.user.tenants?.[0];
    if (!currentTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    // Check permission for proposal updating
    const hasAccess = await hasPermission(
      PermissionResource.PROPOSALS,
      PermissionAction.UPDATE,
      currentTenant.id
    );

    if (!hasAccess) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id: proposalId, sectionId } = params;
    const body = await request.json();
    
    // Validate request data
    const validatedData = enhanceSectionSchema.parse(body);

    // Verify section exists and belongs to tenant
    const existingSection = await prisma.proposalSection.findFirst({
      where: {
        id: sectionId,
        proposalId,
        tenantId: currentTenant.id
      },
      include: {
        proposal: {
          include: {
            opportunity: true
          }
        }
      }
    });

    if (!existingSection) {
      return NextResponse.json(
        { error: 'Section not found or access denied' },
        { status: 404 }
      );
    }

    if (!existingSection.content) {
      return NextResponse.json(
        { error: 'Section has no content to enhance' },
        { status: 400 }
      );
    }

    // Use provided locale or detect the language of the original content
    // If locale is provided, use it; otherwise detect from content
    const proposalLocale = validatedData.locale || (existingSection.proposal.locale as 'en' | 'ar') || 'en';
    const detectedLanguage = proposalLocale === 'ar' ? 'ar' : detectLanguage(existingSection.content);
    console.log(`Using language for section ${sectionId}:`, detectedLanguage, `(proposal locale: ${proposalLocale})`);

    // Get file contexts if file IDs are provided
    let fileContexts: GeminiFileContext[] = [];
    let fileUris: string[] = [];

    if (validatedData.selectedFileIds && validatedData.selectedFileIds.length > 0) {
      try {
        // Get proposal's Gemini file URIs
        const proposal = await prisma.proposal.findFirst({
          where: {
            id: proposalId,
            tenantId: currentTenant.id
          }
        });

        const geminiFileUris = (proposal?.geminiFileUris as Record<string, string>) || {};

        // Get file contexts for the selected files
        fileContexts = await geminiFileService.getFileContexts(
          validatedData.selectedFileIds,
          currentTenant.id,
          geminiFileUris
        );

        // Extract active file URIs
        fileUris = fileContexts
          .filter(ctx => ctx.fileUri && ctx.uploadStatus === 'active')
          .map(ctx => ctx.fileUri);

        console.log(`Using ${fileUris.length} file contexts for enhancement`);
      } catch (error) {
        console.warn('Failed to load file contexts for enhancement:', error);
        // Continue without file context rather than failing
      }
    }

    // Generate enhancement prompt with language preservation and file context
    const enhancementPrompt = generateEnhancementPrompt(
      existingSection.content,
      validatedData.enhancementType,
      validatedData.toneType,
      detectedLanguage,
      validatedData.customPrompt,
      fileContexts
    );

    // Call AI service for enhancement with timeout and optimized settings
    const aiResponse = await Promise.race([
      aiService.generateResponse({
        prompt: enhancementPrompt,
        systemPrompt: getEnhancementSystemPrompt(detectedLanguage),
        fileUris: fileUris.length > 0 ? fileUris : undefined,
        context: {
          sectionType: existingSection.sectionType,
          proposalTitle: existingSection.proposal.title,
          opportunityName: existingSection.proposal.opportunity?.description || 'Unknown Opportunity',
          enhancementType: validatedData.enhancementType,
          toneType: validatedData.toneType,
          fileContexts
        },
        tenantId: currentTenant.id,
        userId: session.user.id,
        feature: 'proposal_section_enhancement'
      }, {
        temperature: 0.6, // Lower temperature for more focused responses
        maxTokens: 1200,  // Reduced token limit for faster responses
        model: 'gemini-2.0-flash' // Use faster model
      }),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('AI request timeout')), 25000) // 25 second timeout
      )
    ]);

    // Parse the enhanced content
    const enhancedContent = parseEnhancedContent(aiResponse.content);

    // Validate language consistency
    const languageConsistency = validateLanguageConsistency(existingSection.content, enhancedContent);

    // Log language validation results
    if (!languageConsistency.isConsistent) {
      console.warn(`Language consistency warning for section ${sectionId}:`, languageConsistency.warning);
    }

    // Calculate word count change
    const originalWordCount = existingSection.content.split(/\s+/).length;
    const enhancedWordCount = enhancedContent.split(/\s+/).length;
    const wordCountChange = enhancedWordCount - originalWordCount;

    const response: EnhancementResponse = {
      originalContent: existingSection.content,
      enhancedContent,
      enhancementType: validatedData.enhancementType,
      toneType: validatedData.toneType,
      customPrompt: validatedData.customPrompt,
      explanation: getEnhancementExplanation(validatedData.enhancementType, validatedData.toneType, wordCountChange, detectedLanguage, validatedData.customPrompt),
      wordCountChange,
      tokensUsed: aiResponse.tokensUsed,
      detectedLanguage,
      languageConsistency
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Section enhancement error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    // Handle timeout errors specifically
    if (error instanceof Error && error.message.includes('timeout')) {
      return NextResponse.json(
        { error: 'Enhancement request timed out. Please try again with shorter content or try later.' },
        { status: 408 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to enhance section content. Please try again.' },
      { status: 500 }
    );
  }
}

function generateEnhancementPrompt(
  content: string,
  enhancementType: EnhancementType,
  toneType: ToneType | undefined,
  detectedLanguage: SupportedLanguage,
  customPrompt?: string,
  fileContexts?: GeminiFileContext[]
): string {
  // Get language-specific instructions
  const languageInstructions = getLanguageInstructions(detectedLanguage);

  let basePrompt = '';

  switch (enhancementType) {
    case 'make_shorter':
      basePrompt = `Condense this content, keeping all key information:

${content}

Make it 20-40% shorter while preserving meaning and structure.

${languageInstructions}`;
      break;

    case 'make_longer':
      basePrompt = `Expand this content with relevant details and examples:

${content}

Add 30-60% more content while maintaining quality and coherence.

${languageInstructions}`;
      break;

    case 'change_tone':
      const toneMap = {
        professional: 'formal business language',
        casual: 'conversational friendly language',
        technical: 'detailed technical language'
      };

      basePrompt = `Rewrite this content using ${toneMap[toneType || 'professional']}:

${content}

Maintain all facts while adjusting the tone.

${languageInstructions}`;
      break;

    case 'custom_enhance':
      basePrompt = `Enhance this content according to the following instructions:

Original Content:
${content}

Enhancement Instructions: ${customPrompt || 'Improve the content quality and clarity'}

${languageInstructions}`;
      break;
  }

  // Add file context if available
  if (fileContexts && fileContexts.length > 0) {
    const fileContext = fileContexts
      .filter(ctx => ctx.uploadStatus === 'active')
      .map(ctx => `- ${ctx.name}`)
      .join('\n');

    if (fileContext) {
      basePrompt += `\n\nReference Documents Available:\n${fileContext}\n\nUse information from these documents to enhance the content where relevant.`;
    }
  }

  // Add additional custom instructions for non-custom enhancement types
  if (enhancementType !== 'custom_enhance' && customPrompt) {
    basePrompt += `\n\nAdditional instructions: ${customPrompt}`;
  }

  return basePrompt;
}

function getEnhancementSystemPrompt(detectedLanguage: SupportedLanguage): string {
  const languageInstructions = getLanguageInstructions(detectedLanguage);

  return `You are a multilingual proposal content enhancer. Your task is to improve content while maintaining accuracy and language consistency.

${languageInstructions}

IMPORTANT RULES:
- Return ONLY the enhanced content without any preambles, explanations, or conversational phrases
- Do NOT add phrases like "Here's the enhanced version" or "I'll help you with..."
- Maintain the exact same language as the original content
- Preserve all factual information and technical accuracy
- Keep the same writing style and formality level
- Focus on the specific enhancement requested

Detected language: ${getLanguageName(detectedLanguage)}`;
}

function parseEnhancedContent(aiResponse: string): string {
  // Remove any conversational preambles or explanations
  const lines = aiResponse.split('\n');
  const contentLines = lines.filter(line => 
    !line.toLowerCase().includes('here\'s') &&
    !line.toLowerCase().includes('enhanced version') &&
    !line.toLowerCase().includes('improved content') &&
    line.trim() !== ''
  );
  
  return contentLines.join('\n').trim();
}

function getEnhancementExplanation(
  enhancementType: EnhancementType,
  toneType: ToneType | undefined,
  wordCountChange: number,
  detectedLanguage: SupportedLanguage,
  customPrompt?: string
): string {
  const languageName = getLanguageName(detectedLanguage);

  switch (enhancementType) {
    case 'make_shorter':
      return `Content condensed to focus on key points in ${languageName}. Word count ${wordCountChange > 0 ? 'increased' : 'reduced'} by ${Math.abs(wordCountChange)} words.`;
    case 'make_longer':
      return `Content expanded with additional details and examples in ${languageName}. Word count increased by ${wordCountChange} words.`;
    case 'change_tone':
      return `Content tone adjusted to ${toneType} style in ${languageName} while preserving all key information.`;
    case 'custom_enhance':
      const instruction = customPrompt ? ` following the instruction: "${customPrompt.substring(0, 100)}${customPrompt.length > 100 ? '...' : ''}"` : '';
      return `Content enhanced using custom AI instructions${instruction} in ${languageName}. Word count ${wordCountChange > 0 ? 'increased' : 'reduced'} by ${Math.abs(wordCountChange)} words.`;
    default:
      return `Content enhanced using AI optimization in ${languageName}.`;
  }
}
